@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-color: 13, 13, 23;
  --background-hex: #0d0d17;
  --neon-purple: 139, 92, 246;
  --neon-pink: 236, 72, 153;
  --neon-blue: 59, 130, 246;
  --neon-indigo: 99, 102, 241;
  --neon-green: 74, 222, 128;
  --laser-purple: 128, 0, 255;
  --laser-pink: 236, 64, 122;
  --laser-blue: 30, 144, 255;
  --high-contrast-text: 240, 240, 252;
}

body {
  color: rgb(var(--foreground-rgb));
  background-color: var(--background-hex);
  position: relative;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Remove any body::before that might cause banding */
body::before {
  display: none !important;
}

/* =================== LASER NEON EFFECTS =================== */

/* Section containers with consistent backgrounds - NO TRANSPARENCY */
.laser-section {
  position: relative;
  border: 1px solid rgba(var(--laser-purple), 0.15);
  box-shadow: 0 0 20px rgba(var(--laser-purple), 0.1);
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: var(--background-hex) !important;
  background-image: none !important;
  transition: box-shadow 0.5s ease;
  margin-bottom: 2rem;
}

.laser-section:hover {
  box-shadow: 0 0 30px rgba(var(--laser-purple), 0.15);
}

/* Laser borders for cards - consistent background */
.laser-card {
  position: relative;
  border: 1px solid rgba(var(--laser-purple), 0.2);
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: var(--background-hex);
  box-shadow: 0 0 15px rgba(var(--laser-purple), 0.1);
  transition: all 0.3s ease;
}

.laser-card:hover {
  box-shadow: 0 0 25px rgba(var(--laser-purple), 0.2);
  border-color: rgba(var(--laser-purple), 0.3);
}

/* Gradient buttons */
.laser-button {
  position: relative;
  background: linear-gradient(90deg, rgba(var(--laser-purple), 0.8), rgba(var(--laser-pink), 0.8));
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(var(--laser-purple), 0.4);
  z-index: 1;
}

.laser-button:hover {
  box-shadow: 0 0 20px rgba(var(--laser-purple), 0.6);
  transform: translateY(-2px);
}

/* Gradient text */
.laser-gradient-text {
  background: linear-gradient(90deg, rgba(var(--laser-purple), 1) 0%, rgba(var(--laser-pink), 1) 50%, rgba(var(--laser-blue), 1) 100%);
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.laser-gradient-text::after {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  background: linear-gradient(90deg, rgba(var(--laser-purple), 1) 0%, rgba(var(--laser-pink), 1) 50%, rgba(var(--laser-blue), 1) 100%);
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  filter: blur(6px);
  opacity: 0.5;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Neon flicker animation */
.laser-flicker {
  animation: flicker 3s linear infinite;
}

@keyframes flicker {
  0%, 19.999%, 22%, 62.999%, 64%, 64.999%, 70%, 100% {
    opacity: 1;
  }
  20%, 21.999%, 63%, 63.999%, 65%, 69.999% {
    opacity: 0.5;
  }
}

/* Microinteractions - Icon glow/pulse hover effect */
.laser-icon {
  transition: all 0.3s ease;
}

.laser-icon:hover {
  transform: scale(1.15);
  filter: drop-shadow(0 0 8px rgba(var(--laser-purple), 0.8));
}

/* Animated laser particles with fixed background */
.laser-particles-container {
  position: fixed;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
  opacity: 0.3;
  background-color: var(--background-hex);
}

.laser-particle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.2;
  filter: blur(3px);
  pointer-events: none;
  animation: laser-float linear infinite;
}

.laser-particle-purple {
  background: rgba(var(--laser-purple), 0.4);
  box-shadow: 0 0 10px rgba(var(--laser-purple), 0.5);
}

.laser-particle-pink {
  background: rgba(var(--laser-pink), 0.4);
  box-shadow: 0 0 10px rgba(var(--laser-pink), 0.5);
}

.laser-particle-blue {
  background: rgba(var(--laser-blue), 0.4);
  box-shadow: 0 0 10px rgba(var(--laser-blue), 0.5);
}

@keyframes laser-float {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.2;
  }
  90% {
    opacity: 0.2;
  }
  100% {
    transform: translateY(var(--y-end)) translateX(var(--x-end));
    opacity: 0;
  }
}

/* Neon section divider */
.laser-divider {
  height: 1px;
  width: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--laser-purple), 0.5), transparent);
  position: relative;
  margin: 3rem 0;
}

.laser-divider::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, transparent, rgba(var(--laser-purple), 0.2), transparent);
  filter: blur(2px);
}

/* Scroll reveal animations */
.scroll-reveal {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Neon focus glow for inputs */
.laser-input {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(var(--laser-purple), 0.2);
  border-radius: 0.375rem;
  color: white;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.laser-input:focus {
  outline: none;
  border-color: rgba(var(--laser-purple), 0.5);
  box-shadow: 0 0 0 3px rgba(var(--laser-purple), 0.25);
}

/* RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
}

.rtl .gradient-text,
.rtl .gradient-button {
  direction: rtl;
}

/* Neo-Futuristic Neon Effects */
.neon-text-outline {
  position: relative;
  z-index: 1;
  text-shadow: 
    0 0 5px rgba(var(--neon-purple), 0.5),
    0 0 10px rgba(var(--neon-purple), 0.3),
    0 0 20px rgba(var(--neon-purple), 0.1);
  transition: text-shadow 0.3s ease-in-out;
}

.neon-text-outline:hover {
  text-shadow: 
    0 0 5px rgba(var(--neon-purple), 0.7),
    0 0 10px rgba(var(--neon-purple), 0.5),
    0 0 20px rgba(var(--neon-purple), 0.3),
    0 0 30px rgba(var(--neon-purple), 0.1);
}

.neon-border {
  position: relative;
  border: 1px solid rgba(var(--neon-purple), 0.3);
  box-shadow: 
    0 0 5px rgba(var(--neon-purple), 0.3),
    0 0 10px rgba(var(--neon-purple), 0.2),
    inset 0 0 5px rgba(var(--neon-purple), 0.1);
  transition: all 0.3s ease;
}

.neon-border:hover {
  border-color: rgba(var(--neon-purple), 0.6);
  box-shadow: 
    0 0 5px rgba(var(--neon-purple), 0.5),
    0 0 10px rgba(var(--neon-purple), 0.3),
    0 0 15px rgba(var(--neon-purple), 0.1),
    inset 0 0 5px rgba(var(--neon-purple), 0.2);
}

.neon-card {
  background: rgba(15, 23, 42, 0.7);
  border-radius: 0.5rem;
  border: 1px solid rgba(var(--neon-purple), 0.2);
  box-shadow: 
    0 0 10px rgba(var(--neon-purple), 0.1),
    inset 0 0 5px rgba(var(--neon-purple), 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.neon-card:hover {
  transform: translateY(-3px);
  border-color: rgba(var(--neon-purple), 0.4);
  box-shadow: 
    0 0 15px rgba(var(--neon-purple), 0.2),
    inset 0 0 10px rgba(var(--neon-purple), 0.1);
}

.neon-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(var(--neon-purple), 0.05),
    transparent
  );
  transition: all 0.5s ease;
}

.neon-card:hover::after {
  left: 100%;
}

.animated-bg-shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.15;
  filter: blur(40px);
  animation: float-around 20s linear infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes float-around {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  25%, 50%, 75% {
    transform: translate(0, 0) scale(1);
  }
}

.neon-button {
  position: relative;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(var(--neon-purple), 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  transition: all 0.2s ease;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(var(--neon-purple), 0.2);
  z-index: 1;
}

.neon-button:hover {
  background: rgba(20, 30, 50, 0.8);
  border-color: rgba(var(--neon-purple), 0.6);
  box-shadow: 
    0 0 15px rgba(var(--neon-purple), 0.4),
    0 0 30px rgba(var(--neon-purple), 0.2);
  transform: translateY(-2px);
}

.neon-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(var(--neon-purple), 0.2),
    transparent
  );
  transition: left 0.5s ease;
  z-index: -1;
}

.neon-button:hover::before {
  left: 100%;
}

/* Floating particles */
.floating-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.floating-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(var(--neon-purple), 0.5);
  filter: blur(1px);
  animation: float-particle var(--duration, 15s) ease-in-out infinite;
  opacity: var(--opacity, 0.3);
}

@keyframes float-particle {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: var(--opacity, 0.3);
  }
  25% {
    transform: translate3d(var(--x1, 50px), var(--y1, -30px), 0) scale(1.2);
    opacity: var(--opacity-mid, 0.4);
  }
  50% {
    transform: translate3d(var(--x2, 100px), var(--y2, 50px), 0) scale(1);
    opacity: var(--opacity-peak, 0.5);
  }
  75% {
    transform: translate3d(var(--x3, 50px), var(--y3, 100px), 0) scale(0.8);
    opacity: var(--opacity-mid, 0.4);
  }
}

/* Animated neon glow for icons */
.neon-icon {
  position: relative;
  transition: all 0.3s ease;
}

.neon-icon:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 0 5px rgba(var(--neon-purple), 0.7));
}

.neon-icon-pulse {
  animation: neon-pulse 2s ease-in-out infinite;
}

@keyframes neon-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 2px rgba(var(--neon-purple), 0.5));
  }
  50% {
    filter: drop-shadow(0 0 5px rgba(var(--neon-purple), 0.8));
  }
}

/* Enhanced section transitions */
.neon-section-divider {
  position: relative;
  height: 1px;
  background: linear-gradient(
    90deg, 
    transparent, 
    rgba(var(--neon-purple), 0.3), 
    transparent
  );
  margin: 2rem 0;
  width: 100%;
}

.neon-section-divider::before {
  content: '';
  position: absolute;
  top: -15px;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(var(--neon-purple), 0.05),
    transparent
  );
  pointer-events: none;
}

/* Section background gradients */
.neon-bg-glow {
  position: relative;
  overflow: hidden;
}

.neon-bg-glow::before {
  content: '';
  position: absolute;
  top: 30%;
  left: -10%;
  width: 50%;
  height: 70%;
  background: radial-gradient(
    circle,
    rgba(var(--neon-purple), 0.1) 0%,
    transparent 70%
  );
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 0;
}

.neon-bg-glow:hover::before {
  opacity: 1;
}

/* Gradients */
.gradient-text {
  background: linear-gradient(90deg, #a78bfa, #818cf8, #60a5fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.gradient-button {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed, #6d28d9);
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(123, 97, 255, 0.15);
  animation: pulse-glow 3s infinite alternate;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
  }
  100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
  }
}

.gradient-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.35);
}

/* Non-animated button styles */
.static-button {
  background: #7c3aed;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(123, 97, 255, 0.15);
}

.static-button:hover {
  background: #6d28d9;
}

/* Dots effects */
.dot-blue {
  background-color: #60a5fa;
  transition: transform 0.3s ease;
}

.dot-purple {
  background-color: #8b5cf6;
  transition: transform 0.3s ease;
}

.dot-pink {
  background-color: #ec4899;
  transition: transform 0.3s ease;
}

.dots-container:hover .dot {
  transform: scale(1.3);
}

/* Navigation links */
.nav-link {
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background: linear-gradient(90deg, #8b5cf6, #60a5fa);
  transition: width 0.3s ease;
}

.rtl .nav-link::after {
  left: auto;
  right: 0;
}

.nav-link:hover::after {
  width: 100%;
}

/* AI Badge */
.ai-badge {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.ai-badge:hover {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

.ai-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(139, 92, 246, 0), 
    rgba(139, 92, 246, 0.2), 
    rgba(139, 92, 246, 0));
  animation: pulse 2s infinite;
}

.rtl .ai-badge::before {
  left: auto;
  right: -100%;
  transform: scaleX(-1);
  animation: pulse-rtl 2s infinite;
}

@keyframes pulse {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse-rtl {
  0% {
    right: -100%;
  }
  50% {
    right: 100%;
  }
  100% {
    right: 100%;
  }
}

/* Shake effect */
.shake-on-hover:hover {
  animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
}

@keyframes shake {
  10%, 90% {
    transform: translate3d(-1px, -1px, 0);
  }
  
  20%, 80% {
    transform: translate3d(2px, 2px, 0);
  }

  30%, 50%, 70% {
    transform: translate3d(-2px, -2px, 0);
  }

  40%, 60% {
    transform: translate3d(2px, 2px, 0);
  }
}

/* Glassmorphism */
.glass-effect {
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  background-color: rgba(13, 13, 23, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

/* Fix for Safari */
@supports not ((-webkit-backdrop-filter: blur(8px)) or (backdrop-filter: blur(8px))) {
  .glass-effect {
    background-color: rgba(13, 13, 23, 0.9);
  }
}

/* Add after existing styles */

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Custom scrollbar for a futuristic look */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7);
}

/* Ambient glow effects */
.glow-blue {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
}

.glow-purple {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

.glow-green {
  box-shadow: 0 0 15px rgba(74, 222, 128, 0.5);
}

.glow-yellow {
  box-shadow: 0 0 15px rgba(250, 204, 21, 0.5);
}

.glow-indigo {
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-pulse-glow {
  animation: pulse-glow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Dashboard specific styles */
.bg-grid-pattern {
  background-size: 20px 20px;
  background-image: linear-gradient(to right, rgba(30, 41, 59, 0.3) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(30, 41, 59, 0.3) 1px, transparent 1px);
}

.shadow-neon-blue {
  box-shadow: 0 0 15px -5px rgba(59, 130, 246, 0.3);
}

.shadow-neon-purple {
  box-shadow: 0 0 15px -5px rgba(147, 51, 234, 0.3);
}

.shadow-neon-green {
  box-shadow: 0 0 15px -5px rgba(16, 185, 129, 0.3);
}

.shadow-neon-indigo {
  box-shadow: 0 0 15px -5px rgba(99, 102, 241, 0.3);
}

.shadow-neon-yellow {
  box-shadow: 0 0 15px -5px rgba(245, 158, 11, 0.3);
}

/* Glass effect for cards */
.backdrop-blur-xs {
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}

@layer utilities {
  /* Subtle text shadow for glowing effect */
  .text-shadow-blue {
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.7);
  }
  .text-shadow-purple {
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.7);
  }
  .text-shadow-green {
    text-shadow: 0 0 10px rgba(74, 222, 128, 0.7);
  }
  .text-shadow-indigo {
    text-shadow: 0 0 10px rgba(99, 102, 241, 0.7);
  }
  .text-shadow-yellow {
    text-shadow: 0 0 10px rgba(250, 204, 21, 0.7);
  }
}

/* Dashboard Card Styles - Enhanced for consistency */
.dashboard-card {
  @apply bg-gray-900/90 backdrop-blur-sm rounded-xl border border-gray-800/60 transition-all duration-300 hover:scale-[1.02] relative overflow-hidden;
}

.dashboard-card:hover {
  @apply border-gray-700/80 shadow-lg;
  transform: translateY(-2px) scale(1.01);
}

.dashboard-card-blue {
  @apply bg-gradient-to-br from-blue-900/30 to-slate-900/90 border-blue-700/30;
}

.dashboard-card-purple {
  @apply bg-gradient-to-br from-purple-900/30 to-slate-900/90 border-purple-700/30;
}

.dashboard-card-green {
  @apply bg-gradient-to-br from-green-900/30 to-slate-900/90 border-green-700/30;
}

.dashboard-card-indigo {
  @apply bg-gradient-to-br from-indigo-900/30 to-slate-900/90 border-indigo-700/30;
}

.dashboard-card-yellow {
  @apply bg-gradient-to-br from-yellow-900/30 to-slate-900/90 border-yellow-700/30;
}

.dashboard-card-header {
  @apply flex flex-wrap items-center justify-between mb-4 gap-2;
}

.dashboard-card-title {
  @apply flex items-center text-lg font-medium flex-wrap mr-2;
}

.dashboard-card-title-icon {
  @apply mr-2 p-1.5 rounded-md flex-shrink-0 flex items-center justify-center;
  width: 32px;
  height: 32px;
}

/* Chart Container Styles */
.chart-container {
  @apply relative bg-slate-950/40 rounded-lg p-3 border border-slate-800/50 h-full;
  min-height: 240px;
}

/* Mobile Responsive Adjustments */
@media (max-width: 640px) {
  .chart-container {
    min-height: 200px;
  }
}

/* Card Glow Effects */
.card-glow {
  @apply absolute inset-0 opacity-0 transition-opacity duration-300 pointer-events-none;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
}

.dashboard-card:hover .card-glow {
  @apply opacity-100;
}

/* Fancy Card Border Glow */
.border-glow {
  @apply relative overflow-hidden;
}

.border-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 0.8rem;
  filter: blur(8px);
}

.border-glow:hover::before {
  opacity: 1;
}

.border-glow::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.75rem;
  padding: 1.5px;
  background: linear-gradient(
    135deg,
    rgba(60, 60, 80, 0.5) 0%,
    rgba(160, 160, 200, 0.3) 50%,
    rgba(60, 60, 80, 0.5) 100%
  );
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  z-index: 1;
}

.border-glow:hover::after {
  background: linear-gradient(135deg, rgba(80, 80, 120, 0.8) 0%, rgba(180, 180, 240, 0.6) 50%, rgba(80, 80, 120, 0.8) 100%);
}

/* Shadow Effects for Cards */
.shadow-neon-blue {
  box-shadow: 0 0 15px -5px rgba(59, 130, 246, 0.3);
}

.shadow-neon-purple {
  box-shadow: 0 0 15px -5px rgba(147, 51, 234, 0.3);
}

.shadow-neon-green {
  box-shadow: 0 0 15px -5px rgba(16, 185, 129, 0.3);
}

.shadow-neon-yellow {
  box-shadow: 0 0 15px -5px rgba(245, 158, 11, 0.3);
}

.shadow-neon-indigo {
  box-shadow: 0 0 15px -5px rgba(99, 102, 241, 0.3);
}

/* Metric card sizing helpers */
.metric-card-grid {
  @apply grid gap-4;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  grid-auto-rows: minmax(140px, auto);
}

.metric-card {
  @apply h-full min-h-[140px] flex flex-col;
}

/* Chart panel sizing helpers */
.chart-panel-grid {
  @apply grid gap-6;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  grid-auto-rows: minmax(350px, auto);
}

.chart-panel {
  @apply h-full min-h-[350px] flex flex-col;
}

/* Card Title Icons */
.title-icon-blue {
  @apply p-2 rounded-md bg-blue-900/40 text-blue-400 mr-2 shadow-lg border border-blue-500/20;
}

.title-icon-purple {
  @apply p-2 rounded-md bg-purple-900/40 text-purple-400 mr-2 shadow-lg border border-purple-500/20;
}

.title-icon-green {
  @apply p-2 rounded-md bg-green-900/40 text-green-400 mr-2 shadow-lg border border-green-500/20;
}

.title-icon-indigo {
  @apply p-2 rounded-md bg-indigo-900/40 text-indigo-400 mr-2 shadow-lg border border-indigo-500/20;
}

.title-icon-yellow {
  @apply p-2 rounded-md bg-yellow-900/40 text-yellow-400 mr-2 shadow-lg border border-yellow-500/20;
}

/* Improved text contrast styles */
.high-contrast-text {
  color: rgb(var(--high-contrast-text));
}

.caption-text {
  color: rgba(var(--high-contrast-text), 0.85);
  font-weight: 500;
  letter-spacing: 0.01em;
}

.subheading-text {
  color: rgba(var(--high-contrast-text), 0.9);
  font-weight: 600;
  letter-spacing: 0.02em;
}

/* Enhanced section spacing */
.section-spacing {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

@media (min-width: 768px) {
  .section-spacing {
    margin-top: 4rem;
    margin-bottom: 4rem;
  }
}

/* Primary and secondary CTA styles */
.primary-cta {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  color: white;
  background: linear-gradient(to right, #8b5cf6, #6366f1);
  border-radius: 9999px;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
  border: 2px solid transparent;
  z-index: 1;
}

.primary-cta:hover {
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.35);
  transform: translateY(-2px);
}

.secondary-cta {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  color: white;
  background-color: transparent;
  border: 2px solid rgba(139, 92, 246, 0.6);
  border-radius: 9999px;
  transition: all 0.3s ease;
  overflow: hidden;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.secondary-cta:hover {
  background-color: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.9);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
  transform: translateY(-2px);
}

/* Enhanced information hierarchy */
.heading-xl {
  font-size: 2.75rem;
  line-height: 1.1;
  font-weight: 800;
  margin-bottom: 1.5rem;
  letter-spacing: -0.03em;
}

.heading-lg {
  font-size: 2.25rem;
  line-height: 1.2;
  font-weight: 700;
  margin-bottom: 1.25rem;
  letter-spacing: -0.02em;
}

.heading-md {
  font-size: 1.75rem;
  line-height: 1.3;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
}

@media (min-width: 768px) {
  .heading-xl {
    font-size: 3.75rem;
  }
  
  .heading-lg {
    font-size: 3rem;
  }
  
  .heading-md {
    font-size: 2.25rem;
  }
}

/* Feature highlight styles */
.feature-highlight {
  border-left: 3px solid rgba(var(--laser-purple), 0.7);
  padding-left: 1rem;
  background: linear-gradient(90deg, 
    rgba(var(--laser-purple), 0.1) 0%, 
    rgba(var(--laser-purple), 0) 100%
  );
}

/* Improved carousel navigation */
.carousel-nav {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(var(--high-contrast-text), 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.carousel-dot.active {
  background-color: rgba(var(--laser-purple), 0.9);
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(var(--laser-purple), 0.5);
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(var(--laser-purple), 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(var(--laser-purple), 0.3);
  transition: all 0.3s ease;
  z-index: 10;
}

.carousel-arrow:hover {
  background: rgba(var(--laser-purple), 0.4);
  box-shadow: 0 0 15px rgba(var(--laser-purple), 0.5);
}

.carousel-arrow-left {
  left: -5px;
}

.carousel-arrow-right {
  right: -5px;
}

@media (min-width: 768px) {
  .carousel-arrow-left {
    left: -20px;
  }
  
  .carousel-arrow-right {
    right: -20px;
  }
}

/* Pricing toggle styles */
.pricing-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(var(--laser-purple), 0.15);
  padding: 0.5rem;
  border-radius: 9999px;
  margin: 2rem auto;
}

.pricing-toggle-option {
  padding: 0.5rem 1.5rem;
  border-radius: 9999px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.pricing-toggle-highlight {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  height: calc(100% - 1rem);
  border-radius: 9999px;
  background: linear-gradient(90deg, rgba(var(--laser-purple), 0.7), rgba(var(--laser-pink), 0.7));
  transition: all 0.3s ease;
  z-index: 1;
  box-shadow: 0 0 15px rgba(var(--laser-purple), 0.5);
}

/* Animation for slide in effect */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gentlePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.2);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-pulse-subtle {
  animation: gentlePulse 2s infinite;
}

/* Enhanced conversion rate indicator */
.conversion-change-indicator {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.conversion-change-indicator.positive {
  background-color: rgba(16, 185, 129, 0.2);
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: rgb(74, 222, 128);
}

.conversion-change-indicator.negative {
  background-color: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: rgb(248, 113, 113);
}

@keyframes gentlePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.2);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -500px 0;
  }
  100% {
    background-position: 500px 0;
  }
}

.chart-container {
  position: relative;
  overflow: hidden;
}

.chart-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(165,180,252,0.05) 50%, rgba(255,255,255,0) 100%);
  background-size: 200% 100%;
  animation: shimmer 4s infinite linear;
  pointer-events: none;
}

.conversion-change-indicator {
  /* ... existing code ... */
}

/* Add this new class for unified sections without borders and shadows */
.unified-section {
  position: relative;
  overflow: hidden;
  padding: 1rem 0;
  margin-bottom: 2rem;
  background-color: transparent !important;
  background-image: none !important;
  z-index: 1;
}

/* Mobile Responsiveness Fixes */
@media (max-width: 767px) {
  /* Fix navbar spacing and alignment on mobile */
  motion.nav, nav {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    max-width: 92% !important;
  }

  /* Ensure the logo and text are properly aligned */
  .nav-logo {
    margin-right: 0.25rem !important;
  }
  
  /* Fix alignment of navbar elements on mobile */
  .static-button {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
    font-size: 0.75rem !important;
  }
  
  /* Make the navbar more compact */
  nav {
    padding-top: 0.35rem !important;
    padding-bottom: 0.35rem !important;
  }

  /* Fix hero section text for better readability on mobile */
  .heading-xl {
    font-size: 2rem !important;
    line-height: 1.2 !important;
    letter-spacing: -0.02em !important;
    margin-bottom: 1rem !important;
  }

  .subheading-text {
    font-size: 1rem !important;
    line-height: 1.5 !important;
    margin-bottom: 1.5rem !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  /* Fix spacing for CTA buttons in hero section */
  .hero-cta-container {
    gap: 1rem !important;
    margin-bottom: 2rem !important;
  }

  /* Feature cards fixes for mobile */
  .features-grid {
    gap: 1.5rem !important;
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  .feature-card {
    min-height: auto !important;
    height: auto !important;
    padding: 1.5rem !important;
  }

  .feature-card-icon {
    margin-bottom: 1rem !important;
  }

  .feature-card-highlights {
    margin-top: 1rem !important;
  }

  /* Pricing section mobile fixes */
  .pricing-container {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .pricing-card {
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
  }

  .pricing-card-features {
    margin-top: 1rem !important;
    padding-left: 0.5rem !important;
  }

  .pricing-card-feature {
    margin-bottom: 0.5rem !important;
  }

  .pricing-card-button {
    width: 100% !important;
    margin-top: 1rem !important;
  }

  /* Testimonial section mobile fixes */
  .testimonial-card {
    padding: 1.25rem !important;
    min-height: auto !important;
    margin-bottom: 1rem !important;
    max-width: 100% !important;
    border-radius: 1rem !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  }

  .testimonial-content {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
  }

  .testimonial-author {
    margin-top: 1rem !important;
  }

  /* Mobile testimonial fixes */
  @media (max-width: 640px) {
    .testimonial-card {
      background: linear-gradient(to bottom, #0f0f1f, #1a1a35) !important;
      border: 1px solid rgba(139, 92, 246, 0.2) !important;
    }
    
    .testimonial-card p {
      font-size: 0.875rem !important;
      line-height: 1.5 !important;
    }
    
    .testimonial-card .flex.items-center {
      margin-top: 1rem !important;
    }
    
    #testimonials h2 {
      font-size: 1.75rem !important;
      line-height: 1.3 !important;
    }
    
    #testimonials .subheading-text {
      font-size: 0.875rem !important;
      line-height: 1.4 !important;
      margin-bottom: 1.5rem !important;
    }
  }

  /* General container padding fixes */
  .section-container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* Add classes to specific components that need unique fixes */
.feature-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.feature-description {
  flex: 1;
}

.pricing-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pricing-content {
  flex: 1;
}

.testimonial-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.testimonial-content-wrapper {
  flex: 1;
}

/* Loading pulse animation */
@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.85);
  }
  50% {
    opacity: 0.7;
    transform: scale(1);
  }
}

.loading-pulse {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4f46e5 0%, #8b5cf6 100%);
  animation: pulse 1.5s ease-in-out infinite;
}

/* Hero Animation Styles */
@keyframes soundWave {
  0% {
    height: 10px;
    opacity: 0.5;
  }
  50% {
    height: 35px;
    opacity: 0.8;
  }
  100% {
    height: 10px;
    opacity: 0.5;
  }
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-15px) rotate(5deg);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) rotate(0);
    opacity: 0.6;
  }
}

.phone-frame {
  animation: float 6s ease-in-out infinite;
}

.phone-body {
  box-shadow: 
    0 0 20px rgba(168, 85, 247, 0.3),
    0 0 60px rgba(168, 85, 247, 0.1),
    inset 0 0 4px rgba(168, 85, 247, 0.2);
  transition: all 0.3s ease;
}

.phone-body:hover {
  box-shadow: 
    0 0 25px rgba(168, 85, 247, 0.5),
    0 0 80px rgba(168, 85, 247, 0.2),
    inset 0 0 8px rgba(168, 85, 247, 0.3);
  transform: scale(1.03);
}

.hero-animation-container {
  perspective: 1000px;
}

/* Futuristic Cyberpunk Phone Animation Styles */
.tech-circle {
  position: relative;
  animation: rotate 20s linear infinite;
}

.tech-circle::before,
.tech-circle::after {
  content: '';
  position: absolute;
  inset: -1px;
  border-radius: 50%;
  border: 1px dashed rgba(139, 92, 246, 0.4);
  animation: rotate 30s linear reverse infinite;
}

.tech-circle::after {
  inset: -8px;
  opacity: 0.3;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.data-node {
  animation: pulseGlow 2s ease-in-out infinite alternate;
  box-shadow: 0 0 8px rgba(139, 92, 246, 0.6);
  z-index: 20;
}

@keyframes pulseGlow {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.4);
  }
  100% {
    transform: scale(1.3);
    opacity: 0.8;
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.8);
  }
}

.scanner-line {
  top: -10%;
  animation: scanline 2s ease-in-out infinite;
  opacity: 0.7;
}

@keyframes scanline {
  0% {
    top: -10%;
    opacity: 0;
  }
  20% {
    opacity: 0.8;
  }
  80% {
    opacity: 0.8;
  }
  100% {
    top: 110%;
    opacity: 0;
  }
}

.animate-pulse-slow {
  animation: pulseSlow 4s ease-in-out infinite;
}

@keyframes pulseSlow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-faint {
  animation: pulseFaint 1.5s ease-in-out infinite;
}

@keyframes pulseFaint {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

.voice-waveform .w-1 {
  animation: voiceWave 0.5s ease-in-out infinite alternate;
}

@keyframes voiceWave {
  0% {
    height: 10%;
  }
  50% {
    height: 70%;
  }
  100% {
    height: 30%;
  }
}

.rotating-analysis-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px dashed rgba(139, 92, 246, 0.6);
  transform: translate(-50%, -50%);
  animation: rotateRing 4s linear infinite;
}

.rotating-analysis-ring:nth-child(2) {
  width: 55px;
  height: 55px;
  border-color: rgba(236, 72, 153, 0.5);
  animation-duration: 5s;
  animation-direction: reverse;
}

@keyframes rotateRing {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.pulse-grow {
  animation: pulseGrow 2s ease-in-out infinite alternate;
}

@keyframes pulseGrow {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.4);
  }
  100% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.7);
  }
}

.pulse-subtle {
  animation: pulseSubtle 1.5s ease-in-out infinite;
}

@keyframes pulseSubtle {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.shadow-neon-red {
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
}

.shadow-neon-green {
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.5);
}

.tech-border {
  position: relative;
  overflow: hidden;
}

.tech-border::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background: conic-gradient(
    transparent, 
    rgba(139, 92, 246, 0.3),
    transparent 30%
  );
  animation: borderSpin 8s linear infinite;
}

@keyframes borderSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.sound-bar {
  position: relative;
  overflow: hidden;
}

.sound-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, transparent, rgba(255, 255, 255, 0.4));
}

@keyframes dataFloat {
  0% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-20px) rotate(180deg) scale(1.3);
  }
  100% {
    transform: translateY(0) rotate(360deg) scale(1);
  }
}

.data-particle {
  filter: blur(1px);
  z-index: 5;
}

.drop-shadow-glow {
  filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.6));
}

.holo-overlay {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.05) 0%, 
    rgba(59, 130, 246, 0.05) 50%,
    rgba(236, 72, 153, 0.05) 100%
  );
  mix-blend-mode: overlay;
  z-index: 30;
}

.holo-overlay::before {
  content: '';
  position: absolute;
  inset: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    rgba(139, 92, 246, 0.08) 1px,
    transparent 2px
  );
  background-size: 100% 4px;
  opacity: 0.3;
  z-index: 31;
  pointer-events: none;
}

.scanlines {
  background: repeating-linear-gradient(
    0deg,
    transparent,
    rgba(139, 92, 246, 0.05) 1px,
    transparent 2px
  );
  background-size: 100% 4px;
  mix-blend-mode: overlay;
  animation: scanAnim 8s linear infinite;
  opacity: 0.4;
  z-index: 35;
}

@keyframes scanAnim {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 100px;
  }
}

.code-stream {
  animation: codeStream 20s linear infinite;
  opacity: 0.7;
  z-index: 2;
}

@keyframes codeStream {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  100% {
    transform: translateX(200%) rotate(30deg);
  }
}

/* Orbit Icons Animation */
.orbit-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: orbit var(--orbit-duration, 15s) linear infinite;
  animation-delay: var(--orbit-delay, 0s);
  z-index: 15;
}

@keyframes orbit {
  0% {
    transform: rotate(0deg) translateX(120px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(120px) rotate(-360deg);
  }
}

.shadow-glow {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

/* AI Core Ring */
.ai-core-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top: 2px solid rgba(139, 92, 246, 0.6);
  border-left: 2px solid rgba(236, 72, 153, 0.6);
  border-bottom: 2px solid rgba(59, 130, 246, 0.6);
  animation: coreRingRotate 15s linear infinite;
}

@keyframes coreRingRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Pulse Rings */
.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top: 2px solid rgba(139, 92, 246, 0.5);
  border-right: 2px solid rgba(236, 72, 153, 0.5);
  opacity: 0;
  animation: pulseRingExpand 4s ease-out infinite;
}

@keyframes pulseRingExpand {
  0% {
    width: 150px;
    height: 150px;
    opacity: 0.7;
    border-width: 3px;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
    border-width: 1px;
  }
}

/* Workflow Item Animations */
.workflow-item {
  animation: fadeInUp 0.5s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-workflow-sequence .workflow-item:nth-child(1) {
  animation-delay: 0.3s;
}

.ai-workflow-sequence .workflow-item:nth-child(2) {
  animation-delay: 0.9s;
}

.ai-workflow-sequence .workflow-item:nth-child(3) {
  animation-delay: 1.5s;
}

.ai-workflow-sequence .workflow-item:nth-child(4) {
  animation-delay: 2.1s;
}

/* Add new animation for floating particles */
@keyframes floatParticle {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10px, 10px);
  }
  50% {
    transform: translate(0, 20px);
  }
  75% {
    transform: translate(-10px, 10px);
  }
  100% {
    transform: translate(0, 0);
  }
}

/* Enhanced pulse animation for ambient lighting */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 8s ease-in-out infinite;
}

/* Animation delay classes to fix hydration mismatch */
.animate-pulse-slow-delay-1 {
  animation: pulse-slow 8s ease-in-out infinite;
  animation-delay: 1s;
}

.animate-pulse-slow-delay-2 {
  animation: pulse-slow 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-pulse-slow-delay-0-5 {
  animation: pulse-slow 8s ease-in-out infinite;
  animation-delay: 0.5s;
}

.animate-pulse-slow-delay-1-5 {
  animation: pulse-slow 8s ease-in-out infinite;
  animation-delay: 1.5s;
}

/* Enhanced FallingIcons Animations */
.falling-icon {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* GPU-accelerated falling animation */
@keyframes fallWithDrift {
  0% {
    transform: translate3d(0, -100vh, 0) rotate(0deg) scale(0.8);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translate3d(var(--drift-x, 0), 120vh, 0) rotate(360deg) scale(0.8);
    opacity: 0;
  }
}

/* Glow effect for enhanced visibility */
.icon-glow {
  filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.4))
          drop-shadow(0 0 16px rgba(139, 92, 246, 0.2));
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3),
               0 0 20px rgba(139, 92, 246, 0.2),
               0 0 30px rgba(139, 92, 246, 0.1);
}

/* Performance optimization classes */
.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

/* Responsive icon sizing */
@media (max-width: 640px) {
  .falling-icon {
    font-size: clamp(16px, 4vw, 24px) !important;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .falling-icon {
    font-size: clamp(20px, 3vw, 32px) !important;
  }
}

@media (min-width: 1025px) {
  .falling-icon {
    font-size: clamp(24px, 2.5vw, 40px) !important;
  }
}