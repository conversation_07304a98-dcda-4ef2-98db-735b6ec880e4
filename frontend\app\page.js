"use client";

import { lazy, Suspense, useEffect, useCallback, useState } from 'react';
import Hero from './components/Hero';
import StaticConversationCarousel from './components/StaticConversationCarousel';
import ErrorBoundary from './components/ErrorBoundary';
import FallingIcons from './components/FallingIcons';
import DashboardShowcase from './components/DashboardShowcase';
import dynamic from 'next/dynamic';
const ScrollRevealWrapper = dynamic(() => import('./components/ScrollRevealWrapper'), { ssr: false });
import PricingSection from './components/PricingSection';

// Dynamically import components that use browser APIs
const LottieWithNoSSR = dynamic(() => import('lottie-react'), { ssr: false });
const CallPreviewBox = dynamic(() => import('./components/CallPreviewBox'), { ssr: false });

// Lazy load heavier components
const FeatureCards = lazy(() => import('./components/FeatureCards'));
const TestimonialsSection = lazy(() => import('./components/TestimonialsSection'));
const Footer = lazy(() => import('./components/Footer'));
const ToolsIntegrationSection = lazy(() => import('./components/ToolsIntegrationSection'));

// Lottie Animation Imports
import lostRevenueAnimation from '../public/lottie/lost-revenue.json';
import productivityDrainAnimation from '../public/lottie/productivity-drain.json';
import customerFrustrationAnimation from '../public/lottie/customer-frustration.json';
import satisfactionAnimation from '../public/lottie/satisfaction.json';
import availabilityAnimation from '../public/lottie/availability.json';
import costReductionAnimation from '../public/lottie/cost-reduction.json';
import heroAnimation from '../public/lottie/hero-animation.json';

// Placeholder components for new sections
const ProblemSection = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="container mx-auto py-20 px-4 text-white scroll-reveal relative z-10 text-center">
      {/* Modern ambient lighting effects */}
      <div className="absolute -top-40 right-0 w-[40rem] h-[40rem] rounded-full bg-purple-600/5 blur-[100px] animate-pulse-slow"></div>
      <div className="absolute -bottom-40 -left-20 w-[30rem] h-[30rem] rounded-full bg-pink-600/5 blur-[100px] animate-pulse-slow-delay-2"></div>
      <div className="absolute top-1/3 right-1/3 w-[20rem] h-[20rem] rounded-full bg-blue-600/5 blur-[80px] animate-pulse-slow-delay-1"></div>
      
      <h2 className="heading-lg mb-8 laser-gradient-text mx-auto" data-text="The Real Cost of Missing Customer Connections">
        The Real Cost of Missing Customer Connections
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-6xl mx-auto mt-12">
        {/* Lost Revenue Card - Enhanced with modern glow */}
        <div className="bg-white/5 backdrop-blur-md p-10 rounded-xl border border-purple-500/20 hover:border-purple-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10 relative overflow-hidden group">
          {/* Modern ambient card glow - animates on hover */}
          <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-pink-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full bg-purple-500/10 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          
          <div className="mb-4 bg-pink-500/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto transform-gpu relative">
            <div className="absolute inset-0 rounded-full bg-pink-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            {isClient ? (
              <LottieWithNoSSR 
                animationData={lostRevenueAnimation}
                loop={true} 
                style={{ width: '100%', height: '100%' }} 
                rendererSettings={{
                  preserveAspectRatio: 'xMidYMid slice',
                  progressiveLoad: true
                }}
              />
            ) : (
              <div style={{ width: '100%', height: '100%' }} /> // Placeholder for SSR
            )}
          </div>
          <h3 className="text-xl font-bold text-white mb-4 text-center">Lost Revenue</h3>
          <p className="text-gray-300 text-left">
            <span className="text-pink-400 font-semibold">87%</span> of missed calls never call back. When potential customers can't reach you, they simply move on to your competition—taking their business with them.
          </p>
        </div>
        
        {/* Productivity Drain Card - Enhanced with modern glow */}
        <div className="bg-white/5 backdrop-blur-md p-10 rounded-xl border border-purple-500/20 hover:border-purple-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10 relative overflow-hidden group">
          {/* Modern ambient card glow - animates on hover */}
          <div className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full bg-blue-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-indigo-500/10 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          
          <div className="mb-4 bg-blue-500/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto transform-gpu relative">
            <div className="absolute inset-0 rounded-full bg-blue-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            {isClient ? (
              <LottieWithNoSSR 
                animationData={productivityDrainAnimation}
                loop={true} 
                style={{ width: '100%', height: '100%' }} 
                rendererSettings={{
                  preserveAspectRatio: 'xMidYMid slice',
                  progressiveLoad: true
                }}
              />
            ) : (
              <div style={{ width: '100%', height: '100%' }} /> // Placeholder for SSR
            )}
          </div>
          <h3 className="text-xl font-bold text-white mb-4 text-center">Productivity Drain</h3>
          <p className="text-gray-300 text-left">
            Business owners waste <span className="text-blue-400 font-semibold">25+ hours</span> weekly managing phone calls and voicemails. That's over <span className="text-blue-400 font-semibold">1,300 hours</span> per year spent on low-value admin work.
          </p>
        </div>
        
        {/* Customer Frustration Card - Enhanced with modern glow */}
        <div className="bg-white/5 backdrop-blur-md p-10 rounded-xl border border-purple-500/20 hover:border-purple-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10 relative overflow-hidden group">
          {/* Modern ambient card glow - animates on hover */}
          <div className="absolute -top-20 -left-20 w-40 h-40 rounded-full bg-purple-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="absolute -bottom-20 -right-20 w-40 h-40 rounded-full bg-violet-500/10 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          
          <div className="mb-4 bg-purple-500/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto transform-gpu relative">
            <div className="absolute inset-0 rounded-full bg-purple-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            {isClient ? (
              <LottieWithNoSSR 
                animationData={customerFrustrationAnimation}
                loop={true} 
                style={{ width: '100%', height: '100%' }} 
                rendererSettings={{
                  preserveAspectRatio: 'xMidYMid slice',
                  progressiveLoad: true
                }}
              />
            ) : (
              <div style={{ width: '100%', height: '100%' }} /> // Placeholder for SSR
            )}
          </div>
          <h3 className="text-xl font-bold text-white mb-4 text-center">Customer Frustration</h3>
          <p className="text-gray-300 text-left">
            <span className="text-purple-400 font-semibold">78%</span> of customers have abandoned a business due to poor phone experiences. Today's customers expect immediate, helpful responses—not voicemail or endless holds.
          </p>
        </div>
      </div>
      
      <div className="mt-12 max-w-4xl mx-auto bg-white/5 backdrop-blur-md p-6 rounded-xl border border-purple-500/20 text-center relative overflow-hidden">
        {/* Info box modern ambient glow with layering */}
        <div className="absolute top-0 right-0 w-full h-full bg-gradient-to-br from-yellow-500/5 to-orange-500/5 blur-2xl"></div>
        <div className="absolute bottom-0 left-0 w-2/3 h-2/3 bg-blue-500/5 blur-2xl"></div>
        
        <div className="relative z-10 flex items-center justify-center gap-3 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          <h3 className="text-lg font-semibold">A Recent Harvard Business Study Found:</h3>
        </div>
        <p className="text-gray-300 relative z-10">
          Companies that respond to customer inquiries within 5 minutes are <span className="text-green-400 font-medium">21 times more likely</span> to qualify leads than those that take 30 minutes, and <span className="text-green-400 font-medium">78% more likely</span> to convert them to sales.
        </p>
      </div>
    </div>
  );
};

const FalseSolutionSection = () => (
  <div className="container mx-auto py-20 px-4 text-white scroll-reveal relative z-10 text-center">
    {/* Modern layered ambient lighting effects */}
    <div className="absolute top-0 right-0 w-[35rem] h-[35rem] rounded-full bg-red-600/5 blur-[100px] animate-pulse-slow"></div>
    <div className="absolute bottom-0 left-0 w-[35rem] h-[35rem] rounded-full bg-blue-600/5 blur-[100px] animate-pulse-slow-delay-2"></div>
    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[25rem] h-[25rem] rounded-full bg-purple-600/3 blur-[80px] animate-pulse-slow-delay-1"></div>
    
    <h2 className="heading-lg mb-8 laser-gradient-text mx-auto" data-text="Why Traditional Call Solutions Fail">
      Why Traditional Call Solutions Fail
    </h2>
    <div className="max-w-4xl mx-auto">
      <div className="space-y-6 mt-12">
        <div className="flex flex-col md:flex-row bg-white/5 backdrop-blur-md p-6 rounded-xl border border-red-500/20 hover:border-red-500/30 transition-all duration-300 relative overflow-hidden group">
          {/* Modern solution item ambient glow - animates on hover */}
          <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-red-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
          
          <div className="md:w-16 w-full flex justify-center mr-6 mb-4 md:mb-0">
            <div className="text-red-400 bg-red-500/10 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 relative group-hover:scale-110 transition-transform duration-300">
              <div className="absolute inset-0 rounded-full bg-red-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-bold text-white mb-2">Human Receptionists</h3>
            <p className="text-gray-300">
              Extremely expensive at <span className="text-red-300 font-medium">$38,000+</span> per year. Limited to business hours, need breaks, vacations, and benefits. Still miss calls during busy periods and after hours when many customers prefer to call.
            </p>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row bg-white/5 backdrop-blur-md p-6 rounded-xl border border-red-500/20 hover:border-red-500/30 transition-all duration-300">
          <div className="md:w-16 w-full flex justify-center mr-6 mb-4 md:mb-0">
            <div className="text-red-400 bg-red-500/10 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-bold text-white mb-2">Basic Voicemail & Phone Trees</h3>
            <p className="text-gray-300">
              <span className="text-red-300 font-medium">84%</span> of callers hang up when sent to voicemail, and <span className="text-red-300 font-medium">69%</span> abandon complex phone menus. When customers do leave messages, <span className="text-red-300 font-medium">93%</span> expect a return call within an hour—impossible for most businesses.
            </p>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row bg-white/5 backdrop-blur-md p-6 rounded-xl border border-red-500/20 hover:border-red-500/30 transition-all duration-300">
          <div className="md:w-16 w-full flex justify-center mr-6 mb-4 md:mb-0">
            <div className="text-red-400 bg-red-500/10 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-bold text-white mb-2">Overseas Call Centers</h3>
            <p className="text-gray-300">
              Cost <span className="text-red-300 font-medium">$12-25</span> per hour while frustrating customers with scripted responses, accent barriers, and lack of specific business knowledge. <span className="text-red-300 font-medium">62%</span> of customers report negative experiences with traditional call centers.
            </p>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row bg-white/5 backdrop-blur-md p-6 rounded-xl border border-red-500/20 hover:border-red-500/30 transition-all duration-300">
          <div className="md:w-16 w-full flex justify-center mr-6 mb-4 md:mb-0">
            <div className="text-red-400 bg-red-500/10 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-bold text-white mb-2">Basic AI Chatbots</h3>
            <p className="text-gray-300">
              Limited to text interactions, they can't handle voice calls where <span className="text-red-300 font-medium">67%</span> of high-value conversions happen. Text-only chatbots miss nuance, can't qualify leads effectively, and leave customers feeling frustrated by their limitations.
            </p>
          </div>
        </div>
      </div>
    </div>
    
    <div className="mt-12 max-w-xl mx-auto text-center relative">
      {/* Modern callout box ambient glow */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/15 to-pink-500/15 blur-3xl transform scale-125 animate-pulse-slow"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-fuchsia-500/10 blur-2xl transform rotate-12 scale-110 animate-pulse-slow-delay-1-5"></div>
      
      <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-0.5 rounded-xl relative z-10">
        <div className="bg-gray-900 p-6 rounded-xl relative overflow-hidden">
          {/* Inner glow effect */}
          <div className="absolute top-0 left-0 right-0 h-1/2 bg-white/5 rounded-t-xl"></div>
          
          <h3 className="text-xl font-bold mb-3">The CallSaver Difference:</h3>
          <p className="text-gray-300">
            Our advanced AI actually <span className="text-purple-400 font-medium">talks and listens</span> to your customers, providing human-like voice conversations 24/7—not just text responses or basic phone menus.
          </p>
        </div>
      </div>
    </div>
  </div>
);

const QualificationSection = () => (
  <div className="container mx-auto py-20 px-4 text-center text-white scroll-reveal relative z-10">
    <h2 className="heading-lg mb-8 laser-gradient-text" data-text="Is CallSaver Right For You?">
      Is CallSaver Right For You?
    </h2>
    <p className="subheading-text max-w-3xl mx-auto mb-12">
      CallSaver helps businesses that value every lead and want automated efficiency without hiring more staff.
    </p>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-10 max-w-5xl mx-auto">
      {/* Left Column - Perfect For */}
      <div className="bg-white/5 backdrop-blur-md p-8 rounded-xl border border-green-500/20 hover:border-green-500/30 transition-all duration-300">
        <h3 className="text-xl font-bold text-white mb-6 flex items-center justify-center">
          <span className="text-green-400 bg-green-500/10 w-10 h-10 rounded-full flex items-center justify-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </span>
          Perfect For
        </h3>
        
        <ul className="space-y-4 text-left">
          {[
            'Small businesses with limited staff',
            'Service professionals (plumbers, HVAC, consultants, etc.)',
            'Real estate agents handling multiple clients',
            'Medical & dental practices',
            'Legal firms missing potential client calls',
            'Retail businesses with busy staff',
            'Startups without a dedicated receptionist',
            'Businesses with seasonal call volume peaks'
          ].map((item, index) => (
            <li key={index} className="flex items-start">
              <span className="text-green-400 mr-3 mt-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </span>
              <span className="text-gray-300">{item}</span>
            </li>
          ))}
        </ul>
      </div>
      
      {/* Right Column - Not Ideal For */}
      <div className="bg-white/5 backdrop-blur-md p-8 rounded-xl border border-red-500/20 hover:border-red-500/30 transition-all duration-300">
        <h3 className="text-xl font-bold text-white mb-6 flex items-center justify-center">
          <span className="text-red-400 bg-red-500/10 w-10 h-10 rounded-full flex items-center justify-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </span>
          Not Ideal For
        </h3>
        
        <ul className="space-y-4 text-left">
          {[
            'Enterprises with 24/7 call centers already in place',
            'Businesses requiring specialized emergency response',
            'Organizations needing human-only interactions',
            'Companies with complex security protocols for calls',
            'Businesses with 100% in-person operations only',
            'Industries with strict compliance needs for live operators',
            'Operations without any phone or SMS requirements'
          ].map((item, index) => (
            <li key={index} className="flex items-start">
              <span className="text-red-400 mr-3 mt-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </span>
              <span className="text-gray-300">{item}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  </div>
);

const SectionLoader = () => (
  <div className="w-full h-40 flex items-center justify-center">
    <div className="loading-pulse"></div>
  </div>
);

// Enhancing the Hero Section
const EnhancedHero = () => (
  <div className="container mx-auto px-4 pb-16 pt-20 text-white relative z-10">
    {/* Modern ambient effects for the entire hero section */}
    <div className="absolute -top-40 -left-40 w-96 h-96 rounded-full bg-purple-600/10 blur-3xl animate-pulse-slow"></div>
    <div className="absolute -bottom-20 -right-40 w-96 h-96 rounded-full bg-pink-600/10 blur-3xl animate-pulse-slow-delay-2"></div>
    <div className="absolute top-1/3 left-1/4 w-64 h-64 rounded-full bg-blue-600/5 blur-3xl animate-pulse-slow-delay-1"></div>
    
    <div className="flex flex-col lg:flex-row items-center">
      {/* Left side - Text Content */}
      <div className="lg:w-1/2 lg:pr-12 lg:text-left text-center mb-12 lg:mb-0">
        <div className="mb-6">
          <span className="inline-block bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs px-3 py-1 rounded-full font-medium tracking-wider">AI-POWERED CALL MANAGEMENT</span>
        </div>
        
        <h1 className="heading-xl mb-6 laser-gradient-text" data-text="Let AI Handle Your Calls, You Handle Life.">
          Let AI Handle Your Calls, You Handle Life.
        </h1>
        <h2 className="subheading-text max-w-3xl lg:mx-0 mx-auto mb-10 text-lg">
          CallSaver automates your calls with advanced AI, saving you time and ensuring you never miss important information. Focus on what matters while our AI handles the conversation.
        </h2>
        <div className="flex flex-col sm:flex-row lg:justify-start justify-center gap-4 mb-10">
          <a
            href="#pricing"
            className="btn-primary-lg group relative overflow-hidden"
          >
            <span className="relative z-10">Start Free Trial</span>
            <div className="absolute inset-0 bg-gradient-to-r from-pink-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </a>
          <a
            href="#demo"
            className="btn-secondary-lg group"
          >
            <span className="mr-2">See How It Works</span>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-5 h-5 inline-block">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </a>
        </div>
        <div className="flex flex-wrap lg:justify-start justify-center items-center gap-x-8 gap-y-4 text-gray-400 font-medium text-base">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>Save 5+ hours weekly</span>
          </div>
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>Human-like conversations</span>
          </div>
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>14-Day Free Trial</span>
          </div>
        </div>
      </div>
      
      {/* Right side - AI Call Assistant Preview */}
      <div className="lg:w-1/2 flex justify-center lg:justify-end items-center">
        <CallPreviewBox />
      </div>
    </div>
  </div>
);

// Adding a Voice AI Demo Section
const VoiceAIDemo = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div id="demo" className="container mx-auto py-20 px-4 text-white scroll-reveal relative z-10">
      {/* Modern layered ambient lighting effects */}
    <div className="absolute -top-40 -left-40 w-[40rem] h-[40rem] rounded-full bg-indigo-600/5 blur-[120px] animate-pulse-slow"></div>
    <div className="absolute -bottom-40 -right-40 w-[40rem] h-[40rem] rounded-full bg-purple-600/5 blur-[120px] animate-pulse-slow-delay-2"></div>
    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[30rem] h-[30rem] rounded-full bg-blue-600/3 blur-[100px] animate-pulse-slow-delay-1"></div>
    
    <div className="mb-16 text-center">
      <h2 className="heading-lg mb-6 laser-gradient-text" data-text="Experience Human-Like Voice AI">
        Experience Human-Like Voice AI
      </h2>
      <p className="subheading-text max-w-3xl mx-auto mb-8">
        Our advanced AI voice technology doesn't just respond to calls—it engages in natural conversations, understands context, and provides personalized responses in real-time.
      </p>
    </div>
    
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div className="bg-purple-900/30 backdrop-blur-lg rounded-2xl p-6 border border-purple-500/20 shadow-lg shadow-purple-500/5 relative overflow-hidden group">
        {/* Modern video card ambient glow */}
        <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-purple-500/15 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
        <div className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full bg-pink-500/15 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        
        <div className="relative aspect-video overflow-hidden rounded-lg mb-6">
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900/80 relative">
            {/* Play button glow */}
            <div className="absolute inset-0 w-20 h-20 rounded-full bg-gradient-to-r from-purple-600/30 to-pink-600/30 blur-xl mx-auto my-auto transform scale-150"></div>
            
            <button className="w-20 h-20 flex items-center justify-center rounded-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg relative z-10 group">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white group-hover:scale-110 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
              </svg>
            </button>
          </div>
          <img 
            src="https://placehold.co/600x340/333/white?text=Voice+AI+Demo" 
            alt="CallSaver Voice AI Demo" 
            className="w-full h-full object-cover"
          />
        </div>
        <h3 className="text-xl font-bold mb-4">See how our Voice AI handles real scenarios:</h3>
        <ul className="space-y-3">
          {[
            "Appointment scheduling and rescheduling",
            "Product inquiries and detailed explanations",
            "Customer service issue resolution",
            "Qualifying leads with custom questions",
            "Seamless transfer to human agents when needed"
          ].map((item, i) => (
            <li key={i} className="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-400 mt-1 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-gray-300">{item}</span>
            </li>
          ))}
        </ul>
      </div>
      
      <div className="space-y-8">
        {/* Satisfaction */}
        <div className="bg-white/5 backdrop-blur-md p-6 rounded-xl border border-purple-500/20 hover:border-purple-500/30 transition-all duration-300 relative overflow-hidden group">
          {/* Card ambient glow */}
          <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-purple-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
          
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4 transform-gpu relative">
              <div className="absolute inset-0 rounded-full bg-purple-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              {isClient ? (
                <LottieWithNoSSR 
                  animationData={satisfactionAnimation}
                  loop={true} 
                  style={{ width: '100%', height: '100%' }} 
                  rendererSettings={{
                    preserveAspectRatio: 'xMidYMid slice',
                    progressiveLoad: true
                  }}
                />
              ) : (
                <div style={{ width: '100%', height: '100%' }} /> // Placeholder for SSR
              )}
            </div>
            <h3 className="text-xl font-bold">99.3% Caller Satisfaction</h3>
          </div>
          <p className="text-gray-300">Our Voice AI achieves near-perfect satisfaction rates by accurately understanding caller intent and providing helpful responses.</p>
        </div>
        
        {/* Availability */}
        <div className="bg-white/5 backdrop-blur-md p-6 rounded-xl border border-purple-500/20 hover:border-purple-500/30 transition-all duration-300">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 rounded-full bg-pink-500/20 flex items-center justify-center mr-4 transform-gpu">
              {isClient ? (
                <LottieWithNoSSR 
                  animationData={availabilityAnimation}
                  loop={true} 
                  style={{ width: '100%', height: '100%' }} 
                  rendererSettings={{
                    preserveAspectRatio: 'xMidYMid slice',
                    progressiveLoad: true
                  }}
                />
              ) : (
                <div style={{ width: '100%', height: '100%' }} /> // Placeholder for SSR
              )}
            </div>
            <h3 className="text-xl font-bold">24/7 Availability</h3>
          </div>
          <p className="text-gray-300">Never miss another call. Our Voice AI answers calls around the clock, capturing opportunities even when you're unavailable.</p>
        </div>
        
        {/* Cost Reduction */}
        <div className="bg-white/5 backdrop-blur-md p-6 rounded-xl border border-purple-500/20 hover:border-purple-500/30 transition-all duration-300">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4 transform-gpu">
              {isClient ? (
                <LottieWithNoSSR 
                  animationData={costReductionAnimation}
                  loop={true} 
                  style={{ width: '100%', height: '100%' }} 
                  rendererSettings={{
                    preserveAspectRatio: 'xMidYMid slice',
                    progressiveLoad: true
                  }}
                />
              ) : (
                <div style={{ width: '100%', height: '100%' }} /> // Placeholder for SSR
              )}
            </div>
            <h3 className="text-xl font-bold">70% Cost Reduction</h3>
          </div>
          <p className="text-gray-300">Businesses using our Voice AI report an average 70% reduction in call handling costs compared to traditional receptionist services.</p>
        </div>
      </div>
    </div>
    
    <div className="mt-16 text-center">
      <a 
        href="#pricing" 
        className="btn-primary-lg inline-flex items-center group relative overflow-hidden"
      >
        <span className="relative z-10">Start Your Free Trial</span>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
        </svg>
        <div className="absolute inset-0 bg-gradient-to-r from-pink-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </a>
    </div>
  </div>
  );
};

// Adding Benefits Section
const BenefitsSection = () => (
  <div className="container mx-auto py-20 px-4 text-white scroll-reveal relative z-10">
    {/* Modern ambient lighting effects */}
    <div className="absolute -top-40 left-0 w-[40rem] h-[40rem] rounded-full bg-purple-600/5 blur-[100px] animate-pulse-slow"></div>
    <div className="absolute -bottom-40 right-0 w-[30rem] h-[30rem] rounded-full bg-pink-600/5 blur-[100px] animate-pulse-slow-delay-2"></div>
    <div className="absolute top-1/3 left-1/3 w-[20rem] h-[20rem] rounded-full bg-blue-600/5 blur-[80px] animate-pulse-slow-delay-1"></div>
    
    <h2 className="heading-lg mb-12 laser-gradient-text text-center" data-text="Core Benefits">
      Core Benefits
    </h2>
    
    <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-6xl mx-auto">
      {/* Time Savings Benefit */}
      <div className="bg-white/5 backdrop-blur-md p-8 rounded-xl border border-purple-500/20 hover:border-purple-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10 relative overflow-hidden group">
        {/* Card ambient glow */}
        <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-purple-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
        
        <div className="mb-6 bg-purple-500/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto transform-gpu relative">
          <div className="absolute inset-0 rounded-full bg-purple-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-xl font-bold text-white mb-4 text-center">Time Savings</h3>
        <p className="text-gray-300 text-center">
          Save 5+ hours weekly with AI call management that handles conversations, schedules appointments, and captures important details.
        </p>
      </div>
      
      {/* Never Miss Details Benefit */}
      <div className="bg-white/5 backdrop-blur-md p-8 rounded-xl border border-purple-500/20 hover:border-purple-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10 relative overflow-hidden group">
        {/* Card ambient glow */}
        <div className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full bg-pink-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
        
        <div className="mb-6 bg-pink-500/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto transform-gpu relative">
          <div className="absolute inset-0 rounded-full bg-pink-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        </div>
        <h3 className="text-xl font-bold text-white mb-4 text-center">Never Miss Details</h3>
        <p className="text-gray-300 text-center">
          Every call detail captured and organized with accurate transcriptions, summaries, and action items ready for your review.
        </p>
      </div>
      
      {/* Seamless Integration Benefit */}
      <div className="bg-white/5 backdrop-blur-md p-8 rounded-xl border border-purple-500/20 hover:border-purple-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10 relative overflow-hidden group">
        {/* Card ambient glow */}
        <div className="absolute -top-20 -left-20 w-40 h-40 rounded-full bg-blue-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
        
        <div className="mb-6 bg-blue-500/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto transform-gpu relative">
          <div className="absolute inset-0 rounded-full bg-blue-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <h3 className="text-xl font-bold text-white mb-4 text-center">Seamless Integration</h3>
        <p className="text-gray-300 text-center">
          Works with your existing phone system and integrates with the tools you already use like Google Calendar, Slack, and CRM software.
        </p>
      </div>
    </div>
  </div>
);

// Adding Target Audience Section
const TargetAudienceSection = () => (
  <div className="container mx-auto py-20 px-4 text-white scroll-reveal relative z-10">
    {/* Modern ambient lighting effects */}
    <div className="absolute -top-40 right-0 w-[40rem] h-[40rem] rounded-full bg-indigo-600/5 blur-[100px] animate-pulse-slow"></div>
    <div className="absolute -bottom-40 left-0 w-[30rem] h-[30rem] rounded-full bg-violet-600/5 blur-[100px] animate-pulse-slow-delay-2"></div>
    <div className="absolute top-1/3 right-1/3 w-[20rem] h-[20rem] rounded-full bg-fuchsia-600/5 blur-[80px] animate-pulse-slow-delay-1"></div>
    
    <h2 className="heading-lg mb-12 laser-gradient-text text-center" data-text="Who Benefits From CallSaver?">
      Who Benefits From CallSaver?
    </h2>
    
    <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-6xl mx-auto">
      {/* Busy Professionals */}
      <div className="bg-white/5 backdrop-blur-md p-8 rounded-xl border border-indigo-500/20 hover:border-indigo-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-indigo-500/10 relative overflow-hidden group text-center">
        {/* Card ambient glow */}
        <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-indigo-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
        
        <div className="mb-6 relative">
          <div className="w-24 h-24 mx-auto bg-indigo-500/10 rounded-full flex items-center justify-center relative">
            <div className="absolute inset-0 rounded-full bg-indigo-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
        </div>
        <h3 className="text-xl font-bold text-white mb-4">Busy Professionals</h3>
        <p className="text-gray-300">
          Never miss important client details or lose potential business while you're in meetings or focusing on critical work.
        </p>
      </div>
      
      {/* Small Business Owners */}
      <div className="bg-white/5 backdrop-blur-md p-8 rounded-xl border border-violet-500/20 hover:border-violet-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-violet-500/10 relative overflow-hidden group text-center">
        {/* Card ambient glow */}
        <div className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full bg-violet-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
        
        <div className="mb-6 relative">
          <div className="w-24 h-24 mx-auto bg-violet-500/10 rounded-full flex items-center justify-center relative">
            <div className="absolute inset-0 rounded-full bg-violet-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-violet-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
        </div>
        <h3 className="text-xl font-bold text-white mb-4">Small Business Owners</h3>
        <p className="text-gray-300">
          Handle customer calls without hiring staff, improve response rates, and deliver consistent experiences for every caller.
        </p>
      </div>
      
      {/* Remote Workers */}
      <div className="bg-white/5 backdrop-blur-md p-8 rounded-xl border border-fuchsia-500/20 hover:border-fuchsia-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-fuchsia-500/10 relative overflow-hidden group text-center">
        {/* Card ambient glow */}
        <div className="absolute -top-20 -left-20 w-40 h-40 rounded-full bg-fuchsia-500/10 blur-xl opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>
        
        <div className="mb-6 relative">
          <div className="w-24 h-24 mx-auto bg-fuchsia-500/10 rounded-full flex items-center justify-center relative">
            <div className="absolute inset-0 rounded-full bg-fuchsia-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-fuchsia-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
          </div>
        </div>
        <h3 className="text-xl font-bold text-white mb-4">Remote Workers</h3>
        <p className="text-gray-300">
          Manage calls while focusing on deep work, eliminate distractions, and maintain a professional presence from anywhere.
        </p>
      </div>
    </div>
  </div>
);

// Define TrustIndicators component locally within this file
const TrustIndicators = () => (
  <div className="container mx-auto py-12 px-4 text-center text-white scroll-reveal relative z-10">
    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
      {/* Stats */}
      <div className="p-4">
        <div className="text-3xl font-bold text-purple-400 mb-2">15,000+</div>
        <div className="text-gray-400 text-base">Businesses Served</div>
      </div>
      <div className="p-4">
        <div className="text-3xl font-bold text-pink-400 mb-2">1.2M+</div>
        <div className="text-gray-400 text-base">Calls Handled</div>
      </div>
      <div className="p-4">
        <div className="text-3xl font-bold text-purple-400 mb-2">$43M+</div>
        <div className="text-gray-400 text-base">Revenue Generated</div>
      </div>
      <div className="p-4">
        <div className="text-3xl font-bold text-pink-400 mb-2">4.9/5</div>
        <div className="text-gray-400 text-base">Customer Rating</div>
      </div>
    </div>
    {/* Logos */}
    <div className="mt-12 flex flex-wrap justify-center items-center gap-8 opacity-70">
      {['Forbes', 'TechCrunch', 'Entrepreneur', 'Business Insider', 'CNBC'].map((name, i) => (
        <div key={i} className="text-xl font-bold tracking-tight text-gray-300">{name}</div>
      ))}
    </div>
  </div>
);

export default function Home() {
  // Simplified loading - load all components immediately
  const loadedComponents = {
    features: true,
    voiceAI: true,
    testimonials: true,
    trustIndicators: true,
    footer: true,
    toolsIntegration: true
  };
  
  return (
    <div className="relative overflow-hidden">
      <FallingIcons />

      <ErrorBoundary fallback={<p>Error loading section...</p>}>
        <EnhancedHero />
        <ScrollRevealWrapper>
          <StaticConversationCarousel />
        </ScrollRevealWrapper>
        <ScrollRevealWrapper delay={0.1}>
          <ProblemSection />
        </ScrollRevealWrapper>
        <ScrollRevealWrapper delay={0.1}>
          <FalseSolutionSection />
        </ScrollRevealWrapper>
        <ScrollRevealWrapper delay={0.1}>
          <PricingSection />
        </ScrollRevealWrapper>
        <ScrollRevealWrapper delay={0.1}>
          <QualificationSection />
        </ScrollRevealWrapper>
        <ScrollRevealWrapper delay={0.1}>
          <BenefitsSection />
        </ScrollRevealWrapper>
        <ScrollRevealWrapper delay={0.1}>
          <TargetAudienceSection />
        </ScrollRevealWrapper>
        <Suspense fallback={<SectionLoader />}>
          {loadedComponents.features && <ScrollRevealWrapper delay={0.2}><FeatureCards /></ScrollRevealWrapper>}
          {loadedComponents.toolsIntegration && <ScrollRevealWrapper delay={0.2}><ToolsIntegrationSection /></ScrollRevealWrapper>}
          {loadedComponents.voiceAI && <ScrollRevealWrapper delay={0.2}><VoiceAIDemo /></ScrollRevealWrapper>}
          {loadedComponents.testimonials && <ScrollRevealWrapper delay={0.2}><TestimonialsSection /></ScrollRevealWrapper>}
          {loadedComponents.trustIndicators && <ScrollRevealWrapper delay={0.2}><TrustIndicators /></ScrollRevealWrapper>}
          {loadedComponents.footer && <ScrollRevealWrapper delay={0.3}><Footer /></ScrollRevealWrapper>}
        </Suspense>
      </ErrorBoundary>
    </div>
  );
}
