import { Inter } from 'next/font/google';
import { headers } from 'next/headers';

import './globals.css';

import ClientServiceWorkerManager from './components/ClientServiceWorkerManager';
import ConditionalNavbar from './components/ConditionalNavbar';
import GlobalBackgroundOverlay from './components/GlobalBackgroundOverlay';
import { LanguageProvider } from './i18n/LanguageContext';
import { SessionProvider } from './providers/SessionProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'CallSaver - Never Miss A Customer Call Again',
  description: 'AI-Powered Call Management Platform',
  icons: {
    icon: '/favicon.svg',
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
  openGraph: {
    title: 'CallSaver - Never Miss A Customer Call Again',
    description: 'AI-Powered Call Management Platform',
    type: 'website',
    images: ['/og-image.jpg'],
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#0d0d17',
};

export const revalidate = 3600;

export default function RootLayout({ children }) {
  // Get nonce from headers for CSP
  const headersList = headers();
  const nonce = headersList.get('x-nonce') || '';

  return (
    <html lang="en" className="scroll-smooth" suppressHydrationWarning={true}>
      <head>
        {nonce && (
          <meta name="csp-nonce" content={nonce} />
        )}
      </head>
      <body className={`${inter.className} bg-[#0d0d17] min-h-screen overflow-x-hidden`} suppressHydrationWarning={true}>
        <GlobalBackgroundOverlay />
        <ClientServiceWorkerManager />
        <SessionProvider>
          <LanguageProvider>
            <div className="min-h-screen flex flex-col relative z-10">
              <ConditionalNavbar />
              <main className="flex-grow relative">{children}</main>
            </div>
          </LanguageProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
