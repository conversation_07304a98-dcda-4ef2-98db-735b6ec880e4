<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Section Visibility Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .success {
            background: #22c55e;
        }
        .error {
            background: #ef4444;
        }
        .warning {
            background: #f59e0b;
        }
    </style>
</head>
<body>
    <h1>CallSaver.app Section Visibility Test</h1>
    <div id="results"></div>

    <script>
        function testSectionVisibility() {
            const results = document.getElementById('results');
            const sections = [
                'Hero Section',
                'StaticConversationCarousel',
                'ProblemSection',
                'FalseSolutionSection', 
                'PricingSection',
                'QualificationSection',
                'BenefitsSection',
                'TargetAudienceSection',
                'FeatureCards',
                'ToolsIntegrationSection',
                'VoiceAIDemo',
                'TestimonialsSection',
                'TrustIndicators',
                'Footer'
            ];

            // Test if main page is accessible
            fetch('http://localhost:3000')
                .then(response => {
                    if (response.ok) {
                        results.innerHTML += `<div class="test-result success">✓ Main page is accessible (${response.status})</div>`;
                        
                        // Test for console errors
                        results.innerHTML += `<div class="test-result warning">⚠ Check browser console for JavaScript errors</div>`;
                        
                        // Instructions for manual testing
                        results.innerHTML += `<div class="test-result warning">📋 Manual Test Instructions:</div>`;
                        results.innerHTML += `<div style="margin-left: 20px;">
                            <p>1. Open <a href="http://localhost:3000" target="_blank" style="color: #60a5fa;">http://localhost:3000</a></p>
                            <p>2. Check if you can see sections beyond the hero</p>
                            <p>3. Scroll down to verify all sections are visible</p>
                            <p>4. Open browser console (F12) and check for errors</p>
                            <p>5. Look for "Maximum update depth exceeded" errors</p>
                        </div>`;
                    } else {
                        results.innerHTML += `<div class="test-result error">✗ Main page not accessible (${response.status})</div>`;
                    }
                })
                .catch(error => {
                    results.innerHTML += `<div class="test-result error">✗ Failed to connect to localhost:3000 - ${error.message}</div>`;
                    results.innerHTML += `<div class="test-result warning">Make sure the development server is running with 'npm run dev'</div>`;
                });
        }

        // Run test when page loads
        window.onload = testSectionVisibility;
    </script>
</body>
</html>
