"use client";

import { motion } from 'framer-motion';
import { useState, useEffect, memo, useCallback } from 'react';

/**
 * FallingIcons component - Enhanced with better visibility and performance
 * Creates a background animation of falling icons/particles
 */
function FallingIcons({
  count = 12,
  icons = [
    // Communication & Business Icons
    '✉️', '📱', '💬', '🔔', '📞', '🗓️', '📊', '💼',
    '📧', '📨', '📩', '💌', '📮', '📬', '📭', '📯',
    '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎧', '🔊',
    '💻', '🖥️', '⌨️', '🖱️', '🖨️', '💾', '💿', '📀',
    '📈', '📉', '📋', '📌', '📍', '🎯', '💡', '🔍',
    '⚡', '🌟', '✨', '💫', '🔥', '💎', '🎨', '🚀',
    // Geometric shapes for variety
    '🔷', '🔶', '🔸', '🔹', '🟦', '🟧', '🟨', '🟩'
  ]
}) {
  const [particles, setParticles] = useState([]);
  const [isClient, setIsClient] = useState(false);

  // Responsive particle count based on screen size
  const getResponsiveCount = useCallback(() => {
    if (typeof window === 'undefined') return count;
    const width = window.innerWidth;
    if (width < 640) return Math.min(count * 0.6, 8); // Mobile: fewer particles
    if (width < 1024) return Math.min(count * 0.8, 12); // Tablet: moderate particles
    return Math.min(count, 16); // Desktop: full count, max 16 for performance
  }, [count]);

  // Generate particles only once on mount with cleanup
  useEffect(() => {
    setIsClient(true);

    // Enhanced particle generation with better visibility
    const newParticles = [];
    const particleCount = getResponsiveCount();

    for (let i = 0; i < particleCount; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100,
        y: -10 - Math.random() * 100,
        size: 20 + Math.random() * 20, // Increased from 16-32 to 20-40
        duration: 10 + Math.random() * 8, // Slightly faster for better visibility
        delay: Math.random() * 6,
        rotate: Math.random() * 360,
        icon: icons[Math.floor(Math.random() * icons.length)],
        opacity: 0.3 + Math.random() * 0.3, // Increased from 0.1-0.3 to 0.3-0.6
        glowIntensity: 0.1 + Math.random() * 0.2, // For glow effect
        drift: (Math.random() - 0.5) * 30, // Horizontal drift for more natural movement
      });
    }

    setParticles(newParticles);

    // Cleanup function to prevent memory leaks
    return () => {
      setParticles([]);
    };
  }, [getResponsiveCount, icons]);

  // Performance monitoring - reduce particles on low-end devices
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const checkPerformance = () => {
      // Reduce particles if device has limited memory or is low-end
      if (navigator.deviceMemory && navigator.deviceMemory < 4) {
        // Device has less than 4GB RAM, reduce particles
        const reducedCount = Math.max(4, Math.floor(getResponsiveCount() * 0.5));
        if (particles.length > reducedCount) {
          setParticles(prev => prev.slice(0, reducedCount));
        }
      }
    };

    checkPerformance();
  }, [particles.length, getResponsiveCount]);

  // Don't render anything during SSR or if no particles
  if (!isClient || particles.length === 0) return null;

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="falling-icon icon-glow gpu-accelerated absolute select-none"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            fontSize: `${particle.size}px`,
            color: 'rgba(255, 255, 255, 0.9)',
            '--drift-x': `${particle.drift}px`,
            zIndex: 1,
          }}
          initial={{
            y: particle.y,
            x: particle.x,
            rotate: particle.rotate,
            opacity: 0,
            scale: 0.8
          }}
          animate={{
            y: '120vh',
            x: particle.x + particle.drift, // Add horizontal drift
            rotate: particle.rotate + 360,
            opacity: [0, particle.opacity, particle.opacity, 0],
            scale: [0.8, 1, 1, 0.8]
          }}
          transition={{
            duration: particle.duration,
            delay: particle.delay,
            repeat: Infinity,
            ease: 'linear',
            opacity: { ease: 'easeInOut' },
            scale: { ease: 'easeInOut' }
          }}
        >
          {particle.icon}
        </motion.div>
      ))}
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
export default memo(FallingIcons);
