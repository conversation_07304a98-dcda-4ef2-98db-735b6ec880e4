/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/test-falling-icons/page";
exports.ids = ["app/test-falling-icons/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-falling-icons%2Fpage&page=%2Ftest-falling-icons%2Fpage&appPaths=%2Ftest-falling-icons%2Fpage&pagePath=private-next-app-dir%2Ftest-falling-icons%2Fpage.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-falling-icons%2Fpage&page=%2Ftest-falling-icons%2Fpage&appPaths=%2Ftest-falling-icons%2Fpage&pagePath=private-next-app-dir%2Ftest-falling-icons%2Fpage.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'test-falling-icons',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/test-falling-icons/page.js */ \"(rsc)/./app/test-falling-icons/page.js\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/test-falling-icons/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/test-falling-icons/page\",\n        pathname: \"/test-falling-icons\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-falling-icons%2Fpage&page=%2Ftest-falling-icons%2Fpage&appPaths=%2Ftest-falling-icons%2Fpage&pagePath=private-next-app-dir%2Ftest-falling-icons%2Fpage.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ClientServiceWorkerManager.js */ \"(ssr)/./app/components/ClientServiceWorkerManager.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ConditionalNavbar.jsx */ \"(ssr)/./app/components/ConditionalNavbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/GlobalBackgroundOverlay.jsx */ \"(ssr)/./app/components/GlobalBackgroundOverlay.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/i18n/LanguageContext.jsx */ \"(ssr)/./app/i18n/LanguageContext.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers/SessionProvider.jsx */ \"(ssr)/./app/providers/SessionProvider.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ctest-falling-icons%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ctest-falling-icons%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/test-falling-icons/page.js */ \"(ssr)/./app/test-falling-icons/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FtZXJrJTVDJTVDRG9jdW1lbnRzJTVDJTVDY2FsbHNhdmVyLmFwcCU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDdGVzdC1mYWxsaW5nLWljb25zJTVDJTVDcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEtBQTJIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FsbHNhdmVyNC8/YWJjNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGFtZXJrXFxcXERvY3VtZW50c1xcXFxjYWxsc2F2ZXIuYXBwXFxcXGZyb250ZW5kXFxcXGFwcFxcXFx0ZXN0LWZhbGxpbmctaWNvbnNcXFxccGFnZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ctest-falling-icons%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/ClientServiceWorkerManager.js":
/*!******************************************************!*\
  !*** ./app/components/ClientServiceWorkerManager.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientServiceWorkerManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ClientServiceWorkerManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Unregister service workers in development to prevent caching issues\n        // and \"Frame with ID was removed\" errors during hot reloading.\n        if (false) {}\n    }, []);\n    // This component doesn't render anything\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9DbGllbnRTZXJ2aWNlV29ya2VyTWFuYWdlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7NkRBRWtDO0FBRW5CLFNBQVNDO0lBQ3RCRCxnREFBU0EsQ0FBQztRQUNSLHNFQUFzRTtRQUN0RSwrREFBK0Q7UUFDL0QsSUFBSSxLQUE2REUsRUFBRSxFQVFsRTtJQUNILEdBQUcsRUFBRTtJQUVMLHlDQUF5QztJQUN6QyxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYWxsc2F2ZXI0Ly4vYXBwL2NvbXBvbmVudHMvQ2xpZW50U2VydmljZVdvcmtlck1hbmFnZXIuanM/YzY0OSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDbGllbnRTZXJ2aWNlV29ya2VyTWFuYWdlcigpIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBVbnJlZ2lzdGVyIHNlcnZpY2Ugd29ya2VycyBpbiBkZXZlbG9wbWVudCB0byBwcmV2ZW50IGNhY2hpbmcgaXNzdWVzXG4gICAgLy8gYW5kIFwiRnJhbWUgd2l0aCBJRCB3YXMgcmVtb3ZlZFwiIGVycm9ycyBkdXJpbmcgaG90IHJlbG9hZGluZy5cbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgJ3NlcnZpY2VXb3JrZXInIGluIG5hdmlnYXRvcikge1xuICAgICAgbmF2aWdhdG9yLnNlcnZpY2VXb3JrZXIuZ2V0UmVnaXN0cmF0aW9ucygpLnRoZW4oKHJlZ2lzdHJhdGlvbnMpID0+IHtcbiAgICAgICAgZm9yIChjb25zdCByZWdpc3RyYXRpb24gb2YgcmVnaXN0cmF0aW9ucykge1xuICAgICAgICAgIHJlZ2lzdHJhdGlvbi51bnJlZ2lzdGVyKCk7XG4gICAgICAgIH1cbiAgICAgIH0pLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdTZXJ2aWNlIFdvcmtlciB1bnJlZ2lzdHJhdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgfSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gVGhpcyBjb21wb25lbnQgZG9lc24ndCByZW5kZXIgYW55dGhpbmdcbiAgcmV0dXJuIG51bGw7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiQ2xpZW50U2VydmljZVdvcmtlck1hbmFnZXIiLCJuYXZpZ2F0b3IiLCJzZXJ2aWNlV29ya2VyIiwiZ2V0UmVnaXN0cmF0aW9ucyIsInRoZW4iLCJyZWdpc3RyYXRpb25zIiwicmVnaXN0cmF0aW9uIiwidW5yZWdpc3RlciIsImNhdGNoIiwiZXJyb3IiLCJjb25zb2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ClientServiceWorkerManager.js\n");

/***/ }),

/***/ "(ssr)/./app/components/ConditionalNavbar.jsx":
/*!**********************************************!*\
  !*** ./app/components/ConditionalNavbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConditionalNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./app/components/Navbar.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ConditionalNavbar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    // Apply padding to main content only when navbar is visible\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        pathname\n    ]);\n    // Don't show the navbar on dashboard pages\n    if (pathname && pathname.startsWith(\"/dashboard\")) {\n        return null;\n    }\n    // Show navbar on non-dashboard pages\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ConditionalNavbar.jsx\",\n        lineNumber: 27,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Db25kaXRpb25hbE5hdmJhci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFOEM7QUFDWjtBQUNKO0FBRWYsU0FBU0c7SUFDdEIsTUFBTUMsV0FBV0osNERBQVdBO0lBRTVCLDREQUE0RDtJQUM1REMsZ0RBQVNBLENBQUM7UUFDUixJQUFJLEtBQWtCLEVBQWEsRUFNbEM7SUFDSCxHQUFHO1FBQUNHO0tBQVM7SUFFYiwyQ0FBMkM7SUFDM0MsSUFBSUEsWUFBWUEsU0FBU0MsVUFBVSxDQUFDLGVBQWU7UUFDakQsT0FBTztJQUNUO0lBRUEscUNBQXFDO0lBQ3JDLHFCQUFPLDhEQUFDSCwrQ0FBTUE7Ozs7O0FBQ2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FsbHNhdmVyNC8uL2FwcC9jb21wb25lbnRzL0NvbmRpdGlvbmFsTmF2YmFyLmpzeD9kY2ZkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgTmF2YmFyIGZyb20gJy4vTmF2YmFyJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENvbmRpdGlvbmFsTmF2YmFyKCkge1xyXG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcclxuXHJcbiAgLy8gQXBwbHkgcGFkZGluZyB0byBtYWluIGNvbnRlbnQgb25seSB3aGVuIG5hdmJhciBpcyB2aXNpYmxlXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICBpZiAocGF0aG5hbWUgJiYgIXBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9kYXNoYm9hcmQnKSkge1xyXG4gICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5zZXRQcm9wZXJ0eSgnLS1tYWluLXBhZGRpbmctdG9wJywgJzdyZW0nKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUuc2V0UHJvcGVydHkoJy0tbWFpbi1wYWRkaW5nLXRvcCcsICcwJyk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbcGF0aG5hbWVdKTtcclxuXHJcbiAgLy8gRG9uJ3Qgc2hvdyB0aGUgbmF2YmFyIG9uIGRhc2hib2FyZCBwYWdlc1xyXG4gIGlmIChwYXRobmFtZSAmJiBwYXRobmFtZS5zdGFydHNXaXRoKCcvZGFzaGJvYXJkJykpIHtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxuXHJcbiAgLy8gU2hvdyBuYXZiYXIgb24gbm9uLWRhc2hib2FyZCBwYWdlc1xyXG4gIHJldHVybiA8TmF2YmFyIC8+O1xyXG59Il0sIm5hbWVzIjpbInVzZVBhdGhuYW1lIiwidXNlRWZmZWN0IiwiTmF2YmFyIiwiQ29uZGl0aW9uYWxOYXZiYXIiLCJwYXRobmFtZSIsInN0YXJ0c1dpdGgiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsInN0eWxlIiwic2V0UHJvcGVydHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ConditionalNavbar.jsx\n");

/***/ }),

/***/ "(ssr)/./app/components/FallingIcons.jsx":
/*!*****************************************!*\
  !*** ./app/components/FallingIcons.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/**\n * FallingIcons component - Enhanced with better visibility and performance\n * Creates a background animation of falling icons/particles\n */ function FallingIcons({ count = 12, icons = [\n    // Communication & Business Icons\n    \"✉️\",\n    \"\\uD83D\\uDCF1\",\n    \"\\uD83D\\uDCAC\",\n    \"\\uD83D\\uDD14\",\n    \"\\uD83D\\uDCDE\",\n    \"\\uD83D\\uDDD3️\",\n    \"\\uD83D\\uDCCA\",\n    \"\\uD83D\\uDCBC\",\n    \"\\uD83D\\uDCE7\",\n    \"\\uD83D\\uDCE8\",\n    \"\\uD83D\\uDCE9\",\n    \"\\uD83D\\uDC8C\",\n    \"\\uD83D\\uDCEE\",\n    \"\\uD83D\\uDCEC\",\n    \"\\uD83D\\uDCED\",\n    \"\\uD83D\\uDCEF\",\n    \"☎️\",\n    \"\\uD83D\\uDCDF\",\n    \"\\uD83D\\uDCE0\",\n    \"\\uD83D\\uDCFA\",\n    \"\\uD83D\\uDCFB\",\n    \"\\uD83C\\uDF99️\",\n    \"\\uD83C\\uDFA7\",\n    \"\\uD83D\\uDD0A\",\n    \"\\uD83D\\uDCBB\",\n    \"\\uD83D\\uDDA5️\",\n    \"⌨️\",\n    \"\\uD83D\\uDDB1️\",\n    \"\\uD83D\\uDDA8️\",\n    \"\\uD83D\\uDCBE\",\n    \"\\uD83D\\uDCBF\",\n    \"\\uD83D\\uDCC0\",\n    \"\\uD83D\\uDCC8\",\n    \"\\uD83D\\uDCC9\",\n    \"\\uD83D\\uDCCB\",\n    \"\\uD83D\\uDCCC\",\n    \"\\uD83D\\uDCCD\",\n    \"\\uD83C\\uDFAF\",\n    \"\\uD83D\\uDCA1\",\n    \"\\uD83D\\uDD0D\",\n    \"⚡\",\n    \"\\uD83C\\uDF1F\",\n    \"✨\",\n    \"\\uD83D\\uDCAB\",\n    \"\\uD83D\\uDD25\",\n    \"\\uD83D\\uDC8E\",\n    \"\\uD83C\\uDFA8\",\n    \"\\uD83D\\uDE80\",\n    // Geometric shapes for variety\n    \"\\uD83D\\uDD37\",\n    \"\\uD83D\\uDD36\",\n    \"\\uD83D\\uDD38\",\n    \"\\uD83D\\uDD39\",\n    \"\\uD83D\\uDFE6\",\n    \"\\uD83D\\uDFE7\",\n    \"\\uD83D\\uDFE8\",\n    \"\\uD83D\\uDFE9\"\n] }) {\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Responsive particle count based on screen size\n    const getResponsiveCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (true) return count;\n        const width = window.innerWidth;\n        if (width < 640) return Math.min(count * 0.6, 8); // Mobile: fewer particles\n        if (width < 1024) return Math.min(count * 0.8, 12); // Tablet: moderate particles\n        return Math.min(count, 16); // Desktop: full count, max 16 for performance\n    }, [\n        count\n    ]);\n    // Generate particles only once on mount with cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n        // Enhanced particle generation with better visibility\n        const newParticles = [];\n        const particleCount = getResponsiveCount();\n        for(let i = 0; i < particleCount; i++){\n            newParticles.push({\n                id: i,\n                x: Math.random() * 100,\n                y: -10 - Math.random() * 100,\n                size: 20 + Math.random() * 20,\n                duration: 10 + Math.random() * 8,\n                delay: Math.random() * 6,\n                rotate: Math.random() * 360,\n                icon: icons[Math.floor(Math.random() * icons.length)],\n                opacity: 0.3 + Math.random() * 0.3,\n                glowIntensity: 0.1 + Math.random() * 0.2,\n                drift: (Math.random() - 0.5) * 30\n            });\n        }\n        setParticles(newParticles);\n        // Cleanup function to prevent memory leaks\n        return ()=>{\n            setParticles([]);\n        };\n    }, [\n        getResponsiveCount,\n        icons\n    ]);\n    // Performance monitoring - reduce particles on low-end devices\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) return;\n        const checkPerformance = ()=>{\n            // Reduce particles if device has limited memory or is low-end\n            if (navigator.deviceMemory && navigator.deviceMemory < 4) {\n                // Device has less than 4GB RAM, reduce particles\n                const reducedCount = Math.max(4, Math.floor(getResponsiveCount() * 0.5));\n                if (particles.length > reducedCount) {\n                    setParticles((prev)=>prev.slice(0, reducedCount));\n                }\n            }\n        };\n        checkPerformance();\n    }, [\n        particles.length,\n        getResponsiveCount\n    ]);\n    // Don't render anything during SSR or if no particles\n    if (!isClient || particles.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 pointer-events-none overflow-hidden z-0\",\n        children: particles.map((particle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"falling-icon icon-glow gpu-accelerated absolute select-none\",\n                style: {\n                    left: `${particle.x}%`,\n                    top: `${particle.y}%`,\n                    fontSize: `${particle.size}px`,\n                    color: \"rgba(255, 255, 255, 0.9)\",\n                    \"--drift-x\": `${particle.drift}px`,\n                    zIndex: 1\n                },\n                initial: {\n                    y: particle.y,\n                    x: particle.x,\n                    rotate: particle.rotate,\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    y: \"120vh\",\n                    x: particle.x + particle.drift,\n                    rotate: particle.rotate + 360,\n                    opacity: [\n                        0,\n                        particle.opacity,\n                        particle.opacity,\n                        0\n                    ],\n                    scale: [\n                        0.8,\n                        1,\n                        1,\n                        0.8\n                    ]\n                },\n                transition: {\n                    duration: particle.duration,\n                    delay: particle.delay,\n                    repeat: Infinity,\n                    ease: \"linear\",\n                    opacity: {\n                        ease: \"easeInOut\"\n                    },\n                    scale: {\n                        ease: \"easeInOut\"\n                    }\n                },\n                children: particle.icon\n            }, particle.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FallingIcons.jsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FallingIcons.jsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n// Memoize the component to prevent unnecessary re-renders\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(FallingIcons));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/FallingIcons.jsx\n");

/***/ }),

/***/ "(ssr)/./app/components/GlobalBackgroundOverlay.jsx":
/*!****************************************************!*\
  !*** ./app/components/GlobalBackgroundOverlay.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalBackgroundOverlay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction GlobalBackgroundOverlay() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-[#0d0d17] z-[-5]\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\GlobalBackgroundOverlay.jsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9HbG9iYWxCYWNrZ3JvdW5kT3ZlcmxheS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVlLFNBQVNBO0lBQ3RCLHFCQUNFO2tCQUVFLDRFQUFDQztZQUFJQyxXQUFVOzs7Ozs7O0FBR3JCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FsbHNhdmVyNC8uL2FwcC9jb21wb25lbnRzL0dsb2JhbEJhY2tncm91bmRPdmVybGF5LmpzeD9jNzNjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBHbG9iYWxCYWNrZ3JvdW5kT3ZlcmxheSgpIHtcbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgey8qIFNvbGlkIGJhc2UgYmFja2dyb3VuZCAtIG5vIGV4dHJhIGVsZW1lbnRzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLVsjMGQwZDE3XSB6LVstNV1cIiAvPlxuICAgIDwvPlxuICApO1xufSAiXSwibmFtZXMiOlsiR2xvYmFsQmFja2dyb3VuZE92ZXJsYXkiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/GlobalBackgroundOverlay.jsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Navbar.jsx":
/*!***********************************!*\
  !*** ./app/components/Navbar.jsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../i18n/LanguageContext */ \"(ssr)/./app/i18n/LanguageContext.jsx\");\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/supabaseClient */ \"(ssr)/./app/utils/supabaseClient.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Navbar() {\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const { isRTL } = (0,_i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const supabaseClientRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    // Update scroll position for additional effects\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setScrollY(window.scrollY);\n        };\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Check authentication status\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            try {\n                setIsLoading(true);\n                // Try to get the Supabase client\n                try {\n                    supabaseClientRef.current = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                } catch (error) {\n                    console.error(\"Error getting Supabase client:\", error);\n                    supabaseClientRef.current = null;\n                }\n                // Check if we have a valid client\n                if (supabaseClientRef.current && supabaseClientRef.current.auth) {\n                    const { data } = await supabaseClientRef.current.auth.getSession();\n                    setIsAuthenticated(!!data?.session);\n                } else {\n                    console.error(\"Supabase client or auth not available\");\n                    setIsAuthenticated(false);\n                }\n            } catch (error) {\n                console.error(\"Error checking auth status:\", error);\n                setIsAuthenticated(false);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        checkAuth();\n        // Set up auth state listener with error handling\n        let subscription = null;\n        try {\n            // Only set up listener if we have a valid client\n            if (supabaseClientRef.current && supabaseClientRef.current.auth) {\n                const authListener = supabaseClientRef.current.auth.onAuthStateChange((event, session)=>{\n                    setIsAuthenticated(!!session);\n                });\n                if (authListener && authListener.data) {\n                    subscription = authListener.data.subscription;\n                }\n            }\n        } catch (error) {\n            console.error(\"Error setting up auth listener:\", error);\n        }\n        return ()=>{\n            if (subscription && typeof subscription.unsubscribe === \"function\") {\n                subscription.unsubscribe();\n            }\n        };\n    }, []);\n    // Determine if navbar should be more visible based on scroll\n    const isScrolled = scrollY > 50;\n    // Improved scroll function with better targeting and error handling\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        if (element) {\n            // Calculate header height to adjust scroll position\n            const navHeight = 80; // Approximate height of fixed navbar\n            const elementPosition = element.getBoundingClientRect().top;\n            const offsetPosition = elementPosition + window.pageYOffset - navHeight;\n            window.scrollTo({\n                top: offsetPosition,\n                behavior: \"smooth\"\n            });\n        } else {\n            console.warn(`Section with ID \"${sectionId}\" not found`);\n        }\n        // Close the mobile menu after clicking a link\n        setIsMenuOpen(false);\n    };\n    // Handle sign out\n    const handleSignOut = async ()=>{\n        try {\n            // Try to get the Supabase client if we don't have it yet\n            if (!supabaseClientRef.current) {\n                try {\n                    supabaseClientRef.current = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                } catch (error) {\n                    console.error(\"Error getting Supabase client for sign out:\", error);\n                }\n            }\n            // Check if we have a valid client\n            if (supabaseClientRef.current && supabaseClientRef.current.auth) {\n                await supabaseClientRef.current.auth.signOut();\n            }\n            // Clear any stored demo user\n            if (false) {}\n            router.push(\"/signin\");\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            // Still try to redirect even if there's an error\n            router.push(\"/signin\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: `w-auto max-w-[95%] sm:max-w-[90%] md:max-w-4xl mx-auto flex items-center justify-between py-2 px-2 sm:px-3 md:px-4 fixed top-2 sm:top-4 left-0 right-0 z-50 rounded-full ${isRTL ? \"flex-row-reverse\" : \"flex-row\"}`,\n                style: {\n                    backgroundColor: isScrolled ? \"rgba(13, 13, 23, 0.65)\" : \"rgba(13, 13, 23, 0.65)\",\n                    backdropFilter: \"blur(10px)\",\n                    boxShadow: isScrolled ? \"0 10px 25px rgba(0, 0, 0, 0.15), 0 0 30px rgba(139, 92, 246, 0.15)\" : \"0 8px 20px rgba(0, 0, 0, 0.1), 0 0 20px rgba(139, 92, 246, 0.1)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.08)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center ${isRTL ? \"flex-row-reverse\" : \"flex-row\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: `flex items-center ${isRTL ? \"flex-row-reverse\" : \"flex-row\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `relative w-7 h-7 md:w-9 md:h-9 ${isRTL ? \"ml-1 md:ml-2\" : \"mr-1 md:mr-2\"} nav-logo`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute w-7 h-7 md:w-8 md:h-8 bg-purple-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 md:h-5 md:w-5 text-white\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base md:text-lg font-bold text-white\",\n                                    children: \"CallSaver\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `hidden md:flex items-center justify-center mx-auto ${isRTL ? \"flex-row-reverse space-x-reverse\" : \"flex-row\"} space-x-8`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Home\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection(\"features\"),\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Go to Features section\",\n                                    children: \"Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection(\"pricing\"),\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Go to Pricing section\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/support\",\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Go to Support page\",\n                                    children: \"Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center ml-auto ${isRTL ? \"flex-row-reverse space-x-reverse\" : \"flex-row\"} space-x-2 md:space-x-3`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"block\",\n                                children: !isLoading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard\",\n                                            className: \"mr-2 sm:mr-3 text-white text-xs md:text-sm font-medium hover:text-purple-200 transition-colors\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white text-xs md:text-sm px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 rounded-full font-medium transition-colors shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40\",\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/signin\",\n                                            className: \"mr-2 sm:mr-3 text-white text-xs md:text-sm font-medium hover:text-purple-200 transition-colors\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/signup\",\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white text-xs md:text-sm px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 rounded-full font-medium transition-colors shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"md:hidden flex-shrink-0 ml-1 md:ml-3 text-white p-1.5 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900 rounded-md\",\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                \"aria-label\": \"Toggle mobile menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5 md:h-6 md:w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 md:hidden\",\n                onClick: ()=>setIsMenuOpen(false),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black opacity-50\",\n                        onClick: ()=>setIsMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-16 sm:top-20 right-2 sm:right-4 w-[calc(100%-1rem)] max-w-xs p-4 bg-gray-900/95 backdrop-blur-lg border border-purple-500/20 rounded-xl shadow-2xl z-50 flex flex-col space-y-2\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/\",\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                \"aria-label\": \"Go to Home page\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection(\"features\"),\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                \"aria-label\": \"Go to Features section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Features\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection(\"pricing\"),\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                \"aria-label\": \"Go to Pricing section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Pricing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/support\",\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                \"aria-label\": \"Go to Support page\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Support\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            !isLoading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/dashboard\",\n                                        className: \"text-white py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        \"aria-label\": \"Go to Dashboard\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-2 text-purple-400\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setIsMenuOpen(false);\n                                            handleSignOut();\n                                        },\n                                        className: \"text-white py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors w-full flex items-center justify-center\",\n                                        \"aria-label\": \"Sign Out\",\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/signin\",\n                                        className: \"text-white py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        \"aria-label\": \"Go to Sign In page\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-2 text-purple-400\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Sign In\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/signup\",\n                                        className: \"text-white py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors w-full flex items-center justify-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        \"aria-label\": \"Get Started\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./app/i18n/LanguageContext.jsx":
/*!**************************************!*\
  !*** ./app/i18n/LanguageContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _locales_en_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./locales/en.json */ \"(ssr)/./app/i18n/locales/en.json\");\n/* harmony import */ var _locales_de_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./locales/de.json */ \"(ssr)/./app/i18n/locales/de.json\");\n/* harmony import */ var _locales_ar_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./locales/ar.json */ \"(ssr)/./app/i18n/locales/ar.json\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\n\n\n// Language configurations\nconst LANGUAGES = {\n    en: {\n        code: \"en\",\n        name: \"English\",\n        dir: \"ltr\",\n        translations: _locales_en_json__WEBPACK_IMPORTED_MODULE_2__,\n        flag: \"\\uD83C\\uDDEC\\uD83C\\uDDE7\"\n    },\n    de: {\n        code: \"de\",\n        name: \"Deutsch\",\n        dir: \"ltr\",\n        translations: _locales_de_json__WEBPACK_IMPORTED_MODULE_3__,\n        flag: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\"\n    },\n    ar: {\n        code: \"ar\",\n        name: \"العربية\",\n        dir: \"rtl\",\n        translations: _locales_ar_json__WEBPACK_IMPORTED_MODULE_4__,\n        flag: \"\\uD83C\\uDDE6\\uD83C\\uDDEA\"\n    }\n};\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(LANGUAGES.en);\n    // Detect user's language\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Function to detect language from navigator or localStorage\n        const detectLanguage = ()=>{\n            // Check if there's a stored preference\n            const storedLang = localStorage.getItem(\"preferred-language\");\n            if (storedLang && LANGUAGES[storedLang]) {\n                return LANGUAGES[storedLang];\n            }\n            // Detect browser language\n            const browserLang = navigator.language.split(\"-\")[0].toLowerCase();\n            if (LANGUAGES[browserLang]) {\n                return LANGUAGES[browserLang];\n            }\n            // Default to English\n            return LANGUAGES.en;\n        };\n        // Set the detected language\n        setLanguage(detectLanguage());\n        // Update document direction for RTL support\n        document.documentElement.dir = detectLanguage().dir;\n        if (detectLanguage().dir === \"rtl\") {\n            document.documentElement.classList.add(\"rtl\");\n        } else {\n            document.documentElement.classList.remove(\"rtl\");\n        }\n    }, []);\n    // Update document direction when language changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        document.documentElement.dir = language.dir;\n        if (language.dir === \"rtl\") {\n            document.documentElement.classList.add(\"rtl\");\n        } else {\n            document.documentElement.classList.remove(\"rtl\");\n        }\n        // Store the preference\n        localStorage.setItem(\"preferred-language\", language.code);\n    }, [\n        language\n    ]);\n    // Function to change language\n    const changeLanguage = (langCode)=>{\n        if (LANGUAGES[langCode]) {\n            setLanguage(LANGUAGES[langCode]);\n        }\n    };\n    // Helper function to get a translation by key path\n    const t = (keyPath)=>{\n        const keys = keyPath.split(\".\");\n        let value = language.translations;\n        for (const key of keys){\n            if (value && value[key]) {\n                value = value[key];\n            } else {\n                return keyPath; // Fallback to key if translation not found\n            }\n        }\n        return value;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            changeLanguage,\n            t,\n            languages: LANGUAGES,\n            isRTL: language.dir === \"rtl\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\i18n\\\\LanguageContext.jsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n// Custom hook to use the language context\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (!context) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/i18n/LanguageContext.jsx\n");

/***/ }),

/***/ "(ssr)/./app/providers/SessionProvider.jsx":
/*!*******************************************!*\
  !*** ./app/providers/SessionProvider.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/supabaseClient */ \"(ssr)/./app/utils/supabaseClient.js\");\n/* __next_internal_client_entry_do_not_use__ SessionProvider,useSession,default auto */ \n\n // Import the function\n// Create a context for the session\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction SessionProvider({ children }) {\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const lastEventRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        type: null,\n        timestamp: 0\n    });\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let mounted = true;\n        // Get the initial session\n        const getInitialSession = async ()=>{\n            try {\n                // Check for demo user in localStorage first\n                const demoUser = localStorage.getItem(\"callsaver_demo_user\");\n                if (demoUser) {\n                    console.log(\"Demo user found in localStorage\");\n                    const parsedUser = JSON.parse(demoUser);\n                    // Create a mock session for the demo user\n                    if (mounted) {\n                        setSession({\n                            user: {\n                                id: parsedUser.id,\n                                email: parsedUser.email,\n                                user_metadata: {\n                                    name: parsedUser.name,\n                                    role: parsedUser.role\n                                }\n                            },\n                            expires_at: Date.now() + 24 * 60 * 60 * 1000 // 24 hours from now\n                        });\n                        setLoading(false);\n                    }\n                    return;\n                }\n                // Get the client instance\n                const supabase = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n                if (!supabase) {\n                    console.error(\"Failed to get Supabase client in SessionProvider\");\n                    if (mounted) setLoading(false);\n                    return; // Don't proceed without a client\n                }\n                // Try to get the session from Supabase\n                const { data, error } = await supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting initial session:\", error);\n                    if (mounted) setSession(null);\n                } else {\n                    console.log(\"Initial session check:\", data.session ? \"Session found\" : \"No session\");\n                    if (mounted) setSession(data.session);\n                }\n            } catch (err) {\n                console.error(\"Unexpected error getting session:\", err);\n                if (mounted) setSession(null);\n            } finally{\n                if (mounted) setLoading(false);\n            }\n        };\n        // Debounced session update function\n        const updateSession = (newSession, eventType)=>{\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                if (mounted) {\n                    setSession(newSession);\n                    setLoading(false);\n                    lastEventRef.current = {\n                        type: eventType,\n                        timestamp: Date.now()\n                    };\n                }\n            }, 100); // 100ms debounce\n        };\n        // Get the client instance for the listener\n        const supabase = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        if (!supabase) {\n            console.error(\"Failed to get Supabase client for auth listener\");\n            setLoading(false); // Ensure loading state is updated\n            return;\n        }\n        // Set up auth state listener with improved debouncing\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, newSession)=>{\n            // Skip if it's the same event type within 1 second\n            const now = Date.now();\n            const timeSinceLastEvent = now - lastEventRef.current.timestamp;\n            if (event === lastEventRef.current.type && timeSinceLastEvent < 1000) {\n                console.log(\"Skipping duplicate auth event:\", event);\n                return;\n            }\n            console.log(\"Auth state changed:\", event);\n            // Handle specific auth events\n            switch(event){\n                case \"SIGNED_IN\":\n                    updateSession(newSession, event);\n                    break;\n                case \"SIGNED_OUT\":\n                    updateSession(null, event);\n                    break;\n                case \"TOKEN_REFRESHED\":\n                case \"USER_UPDATED\":\n                    if (newSession) {\n                        updateSession(newSession, event);\n                    }\n                    break;\n                default:\n                    // For other events, only update if there's a meaningful change\n                    if (newSession?.user?.id !== session?.user?.id) {\n                        updateSession(newSession, event);\n                    }\n                    break;\n            }\n        });\n        getInitialSession();\n        // Clean up\n        return ()=>{\n            mounted = false;\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            subscription?.unsubscribe();\n        };\n    }, []);\n    const value = {\n        session,\n        loading,\n        isAuthenticated: !!session,\n        user: session?.user || null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\providers\\\\SessionProvider.jsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n// Hook to use the session context\nfunction useSession() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SessionContext);\n    if (!context) {\n        throw new Error(\"useSession must be used within a SessionProvider\");\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SessionProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers/SessionProvider.jsx\n");

/***/ }),

/***/ "(ssr)/./app/test-falling-icons/page.js":
/*!****************************************!*\
  !*** ./app/test-falling-icons/page.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestFallingIcons)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_FallingIcons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/FallingIcons */ \"(ssr)/./app/components/FallingIcons.jsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction TestFallingIcons() {\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(12);\n    const [showStats, setShowStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Test different icon sets\n    const iconSets = {\n        default: [\n            \"✉️\",\n            \"\\uD83D\\uDCF1\",\n            \"\\uD83D\\uDCAC\",\n            \"\\uD83D\\uDD14\",\n            \"\\uD83D\\uDCDE\",\n            \"\\uD83D\\uDDD3️\",\n            \"\\uD83D\\uDCCA\",\n            \"\\uD83D\\uDCBC\",\n            \"\\uD83D\\uDCE7\",\n            \"\\uD83D\\uDCE8\",\n            \"\\uD83D\\uDCE9\",\n            \"\\uD83D\\uDC8C\",\n            \"\\uD83D\\uDCEE\",\n            \"\\uD83D\\uDCEC\",\n            \"\\uD83D\\uDCED\",\n            \"\\uD83D\\uDCEF\",\n            \"☎️\",\n            \"\\uD83D\\uDCDF\",\n            \"\\uD83D\\uDCE0\",\n            \"\\uD83D\\uDCFA\",\n            \"\\uD83D\\uDCFB\",\n            \"\\uD83C\\uDF99️\",\n            \"\\uD83C\\uDFA7\",\n            \"\\uD83D\\uDD0A\",\n            \"\\uD83D\\uDCBB\",\n            \"\\uD83D\\uDDA5️\",\n            \"⌨️\",\n            \"\\uD83D\\uDDB1️\",\n            \"\\uD83D\\uDDA8️\",\n            \"\\uD83D\\uDCBE\",\n            \"\\uD83D\\uDCBF\",\n            \"\\uD83D\\uDCC0\",\n            \"\\uD83D\\uDCC8\",\n            \"\\uD83D\\uDCC9\",\n            \"\\uD83D\\uDCCB\",\n            \"\\uD83D\\uDCCC\",\n            \"\\uD83D\\uDCCD\",\n            \"\\uD83C\\uDFAF\",\n            \"\\uD83D\\uDCA1\",\n            \"\\uD83D\\uDD0D\",\n            \"⚡\",\n            \"\\uD83C\\uDF1F\",\n            \"✨\",\n            \"\\uD83D\\uDCAB\",\n            \"\\uD83D\\uDD25\",\n            \"\\uD83D\\uDC8E\",\n            \"\\uD83C\\uDFA8\",\n            \"\\uD83D\\uDE80\",\n            \"\\uD83D\\uDD37\",\n            \"\\uD83D\\uDD36\",\n            \"\\uD83D\\uDD38\",\n            \"\\uD83D\\uDD39\",\n            \"\\uD83D\\uDFE6\",\n            \"\\uD83D\\uDFE7\",\n            \"\\uD83D\\uDFE8\",\n            \"\\uD83D\\uDFE9\"\n        ],\n        minimal: [\n            \"\\uD83D\\uDCF1\",\n            \"\\uD83D\\uDCAC\",\n            \"\\uD83D\\uDCDE\",\n            \"✉️\",\n            \"\\uD83D\\uDD14\"\n        ],\n        business: [\n            \"\\uD83D\\uDCBC\",\n            \"\\uD83D\\uDCCA\",\n            \"\\uD83D\\uDCC8\",\n            \"\\uD83D\\uDCBB\",\n            \"\\uD83C\\uDFAF\",\n            \"\\uD83D\\uDCA1\",\n            \"\\uD83D\\uDE80\"\n        ],\n        communication: [\n            \"\\uD83D\\uDCF1\",\n            \"\\uD83D\\uDCDE\",\n            \"✉️\",\n            \"\\uD83D\\uDCAC\",\n            \"\\uD83D\\uDCE7\",\n            \"\\uD83D\\uDCE8\",\n            \"\\uD83D\\uDD14\",\n            \"\\uD83D\\uDCEF\"\n        ]\n    };\n    const [currentIconSet, setCurrentIconSet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"default\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FallingIcons__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                count: count,\n                icons: iconSets[currentIconSet]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-6 left-6 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    href: \"/\",\n                    className: \"flex items-center text-white hover:text-purple-300 transition-colors bg-black/20 backdrop-blur-sm rounded-lg px-4 py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            className: \"h-5 w-5 mr-2\",\n                            viewBox: \"0 0 20 20\",\n                            fill: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        \"Back to Home\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-6 right-6 z-20 bg-black/20 backdrop-blur-sm rounded-lg p-6 text-white max-w-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4 text-purple-300\",\n                        children: \"FallingIcons Test\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-2\",\n                                children: [\n                                    \"Particle Count: \",\n                                    count\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"range\",\n                                min: \"4\",\n                                max: \"20\",\n                                value: count,\n                                onChange: (e)=>setCount(parseInt(e.target.value)),\n                                className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-2\",\n                                children: \"Icon Set:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: currentIconSet,\n                                onChange: (e)=>setCurrentIconSet(e.target.value),\n                                className: \"w-full bg-gray-700 text-white rounded-lg px-3 py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"default\",\n                                        children: \"Default (All Icons)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"minimal\",\n                                        children: \"Minimal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"business\",\n                                        children: \"Business\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"communication\",\n                                        children: \"Communication\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowStats(!showStats),\n                            className: \"w-full bg-purple-600 hover:bg-purple-700 text-white rounded-lg px-4 py-2 transition-colors\",\n                            children: [\n                                showStats ? \"Hide\" : \"Show\",\n                                \" Performance Stats\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    showStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs space-y-2 bg-gray-800/50 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Screen Width: \",\n                                     false ? 0 : \"N/A\",\n                                    \"px\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Device Memory: \",\n                                    typeof navigator !== \"undefined\" && navigator.deviceMemory ? `${navigator.deviceMemory}GB` : \"Unknown\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Active Particles: \",\n                                    count\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Icon Set: \",\n                                    currentIconSet,\n                                    \" (\",\n                                    iconSets[currentIconSet].length,\n                                    \" icons)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-white z-10 relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold mb-6 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\",\n                            children: \"Enhanced FallingIcons Test\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl mb-8 text-gray-300 max-w-2xl mx-auto\",\n                            children: \"This page demonstrates the enhanced FallingIcons component with improved visibility, expanded icon variety, performance optimizations, and responsive design.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12 max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3 text-purple-300\",\n                                            children: \"✨ Enhanced Visibility\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Icons are now 3x more visible with increased opacity (0.3-0.6), larger sizes (20-40px), and subtle glow effects.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3 text-purple-300\",\n                                            children: \"\\uD83C\\uDFAF More Icon Variety\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Expanded from 8 to 48+ business and communication icons including phones, emails, charts, and geometric shapes.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3 text-purple-300\",\n                                            children: \"⚡ Performance Optimized\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"GPU acceleration, responsive particle counts, memory monitoring, and automatic cleanup prevent performance issues.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\test-falling-icons\\\\page.js\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/test-falling-icons/page.js\n");

/***/ }),

/***/ "(ssr)/./app/utils/supabaseClient.js":
/*!*************************************!*\
  !*** ./app/utils/supabaseClient.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ getSupabaseClient,default auto */ \n// Global variable to hold the singleton instance\nlet clientInstance = null;\n// Flag to track initialization in progress\nlet initializationInProgress = false;\n// Flag to indicate if an initialization attempt has been made\nlet initializationAttempted = false;\n// Create a simple mock client that won't throw errors\nconst createMockClient = (reason)=>{\n    console.warn(`Creating mock Supabase client: ${reason}`);\n    return {\n        auth: {\n            getSession: async ()=>({\n                    data: {\n                        session: null\n                    },\n                    error: null\n                }),\n            signInWithPassword: async ()=>({\n                    data: null,\n                    error: new Error(`Supabase client unavailable: ${reason}`)\n                }),\n            signOut: async ()=>({\n                    error: null\n                }),\n            onAuthStateChange: ()=>({\n                    data: {\n                        subscription: {\n                            unsubscribe: ()=>{}\n                        }\n                    }\n                })\n        },\n        // Add minimal implementations for other commonly used methods\n        from: ()=>({\n                select: ()=>({\n                        data: [],\n                        error: null\n                    }),\n                insert: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    }),\n                update: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    }),\n                delete: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    })\n            })\n    };\n};\n// Function to create or return the singleton Supabase client instance\nconst getSupabaseClient = ()=>{\n    // Return existing instance if already created and valid\n    if (clientInstance && clientInstance.auth && typeof clientInstance.auth.getSession === \"function\") {\n        return clientInstance;\n    }\n    // If initialization is already in progress, wait for it to complete\n    if (initializationInProgress) {\n        throw new Error(\"Supabase client initialization in progress. Please retry your operation.\");\n    }\n    // Ensure this runs only on the client\n    if (true) {\n        throw new Error(\"Supabase client can only be initialized in browser environment\");\n    }\n    // Set flag to indicate we're attempting initialization\n    initializationInProgress = true;\n    initializationAttempted = true;\n    try {\n        const supabaseUrl = \"https://tlylzhcdynxbqxiihipe.supabase.co\";\n        const supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRseWx6aGNkeW54YnF4aWloaXBlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMyMDU2OTIsImV4cCI6MjA1ODc4MTY5Mn0.oWZ7PSgHRyU9iu2hqc5GEwxC0g8NveB-5ZhqRrlLn8Y\";\n        if (!supabaseUrl || !supabaseAnonKey) {\n            initializationInProgress = false;\n            throw new Error(\"Supabase credentials are missing in environment variables\");\n        }\n        // Create the actual client instance\n        const newClient = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n            auth: {\n                flowType: \"pkce\",\n                detectSessionInUrl: true,\n                persistSession: true\n            }\n        });\n        // Validate the created client\n        if (!newClient || !newClient.auth || typeof newClient.auth.getSession !== \"function\") {\n            throw new Error(\"Created client is invalid or missing auth methods\");\n        }\n        // Set up auth state change listener for better debugging (optional here)\n        newClient.auth.onAuthStateChange((event, session)=>{\n            console.log(\"[getSupabaseClient] Auth state changed:\", event, session ? \"Session exists\" : \"No session\");\n            // Example: Update local storage flag on sign-in/sign-out\n            // Ensure window check wraps localStorage access\n            if (false) {}\n        });\n        // Store the instance globally\n        clientInstance = newClient;\n        initializationInProgress = false;\n        console.log(\"Supabase client initialized successfully.\");\n        return clientInstance;\n    } catch (error) {\n        console.error(\"Failed to initialize Supabase client:\", error);\n        clientInstance = null; // Ensure instance is null on error\n        initializationInProgress = false;\n        throw error; // Throw the error instead of returning a mock client\n    }\n};\n// Default export the function for easy import\n// Note: Files importing this will need to change from `import supabaseClient from ...`\n// to `import getSupabaseClient from ...` and call `getSupabaseClient()`\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getSupabaseClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/utils/supabaseClient.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"80e66cafe106\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYWxsc2F2ZXI0Ly4vYXBwL2dsb2JhbHMuY3NzPzZhODYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4MGU2NmNhZmUxMDZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/components/ClientServiceWorkerManager.js":
/*!******************************************************!*\
  !*** ./app/components/ClientServiceWorkerManager.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\ClientServiceWorkerManager.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\ClientServiceWorkerManager.js#default`));


/***/ }),

/***/ "(rsc)/./app/components/ConditionalNavbar.jsx":
/*!**********************************************!*\
  !*** ./app/components/ConditionalNavbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\ConditionalNavbar.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\ConditionalNavbar.jsx#default`));


/***/ }),

/***/ "(rsc)/./app/components/GlobalBackgroundOverlay.jsx":
/*!****************************************************!*\
  !*** ./app/components/GlobalBackgroundOverlay.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\GlobalBackgroundOverlay.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\GlobalBackgroundOverlay.jsx#default`));


/***/ }),

/***/ "(rsc)/./app/i18n/LanguageContext.jsx":
/*!**************************************!*\
  !*** ./app/i18n/LanguageContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\i18n\LanguageContext.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\i18n\LanguageContext.jsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\i18n\LanguageContext.jsx#useLanguage`);


/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   revalidate: () => (/* binding */ revalidate),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ClientServiceWorkerManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ClientServiceWorkerManager */ \"(rsc)/./app/components/ClientServiceWorkerManager.js\");\n/* harmony import */ var _components_ConditionalNavbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ConditionalNavbar */ \"(rsc)/./app/components/ConditionalNavbar.jsx\");\n/* harmony import */ var _components_GlobalBackgroundOverlay__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/GlobalBackgroundOverlay */ \"(rsc)/./app/components/GlobalBackgroundOverlay.jsx\");\n/* harmony import */ var _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./i18n/LanguageContext */ \"(rsc)/./app/i18n/LanguageContext.jsx\");\n/* harmony import */ var _providers_SessionProvider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./providers/SessionProvider */ \"(rsc)/./app/providers/SessionProvider.jsx\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"CallSaver - Never Miss A Customer Call Again\",\n    description: \"AI-Powered Call Management Platform\",\n    icons: {\n        icon: \"/favicon.svg\",\n        shortcut: \"/favicon.svg\",\n        apple: \"/favicon.svg\"\n    },\n    openGraph: {\n        title: \"CallSaver - Never Miss A Customer Call Again\",\n        description: \"AI-Powered Call Management Platform\",\n        type: \"website\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    themeColor: \"#0d0d17\"\n};\nconst revalidate = 3600;\nfunction RootLayout({ children }) {\n    // Get nonce from headers for CSP\n    const headersList = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n    const nonce = headersList.get(\"x-nonce\") || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: nonce && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                    name: \"csp-nonce\",\n                    content: nonce\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className)} bg-[#0d0d17] min-h-screen overflow-x-hidden`,\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlobalBackgroundOverlay__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientServiceWorkerManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_7__.SessionProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_6__.LanguageProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-screen flex flex-col relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConditionalNavbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                        className: \"flex-grow relative\",\n                                        children: children\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.js\n");

/***/ }),

/***/ "(rsc)/./app/providers/SessionProvider.jsx":
/*!*******************************************!*\
  !*** ./app/providers/SessionProvider.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   SessionProvider: () => (/* binding */ e0),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useSession: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\providers\SessionProvider.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\providers\SessionProvider.jsx#SessionProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\providers\SessionProvider.jsx#useSession`);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\providers\SessionProvider.jsx#default`));


/***/ }),

/***/ "(rsc)/./app/test-falling-icons/page.js":
/*!****************************************!*\
  !*** ./app/test-falling-icons/page.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\test-falling-icons\page.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\test-falling-icons\page.js#default`));


/***/ }),

/***/ "(ssr)/./app/i18n/locales/ar.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/ar.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"دع الذكاء الاصطناعي يتعامل مع مكالماتك،","line2":"أنت تتعامل مع الحياة."},"subtitle":"متصل بسلاسة. أنت بلا جهد. يقوم حل الرسائل القصيرة الذكي الخاص بنا بإدارة مكالماتك الفائتة حتى تتمكن من التركيز على ما يهم.","buttons":{"trial":"تسجيل الدخول/التسجيل","pricing":"عرض الأسعار"},"footer":{"poweredBy":"مشغل بواسطة","businesses":"+5000 شركة"}},"navbar":{"features":"المميزات","pricing":"الأسعار","testimonials":"الشهادات","signin":"تسجيل الدخول/التسجيل","languages":{"english":"الإنجليزية","german":"الألمانية","arabic":"العربية"}}}');

/***/ }),

/***/ "(ssr)/./app/i18n/locales/de.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/de.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"Lassen Sie KI Ihre Anrufe verwalten,","line2":"Sie genießen das Leben."},"subtitle":"Nahtlos verbunden. Mühelos Sie. Unsere intelligente SMS-Lösung verwaltet Ihre verpassten Anrufe, damit Sie sich auf das Wesentliche konzentrieren können.","buttons":{"trial":"Anmelden/Registrieren","pricing":"Preise anzeigen"},"footer":{"poweredBy":"betrieben von","businesses":"5000+ Unternehmen"}},"navbar":{"features":"Funktionen","pricing":"Preisgestaltung","testimonials":"Erfahrungsberichte","signin":"Anmelden/Registrieren","languages":{"english":"Englisch","german":"Deutsch","arabic":"Arabisch"}}}');

/***/ }),

/***/ "(ssr)/./app/i18n/locales/en.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/en.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"Let AI Handle Your Calls,","line2":"You Handle Life."},"subtitle":"Seamlessly Connected. Effortlessly You. Our intelligent SMS solution manages your missed calls so you can focus on what matters.","buttons":{"trial":"Try 7 Days Free Trial","pricing":"View Pricing"},"footer":{"poweredBy":"powered by","businesses":"5000+ businesses"}},"navbar":{"features":"Features","pricing":"Pricing","testimonials":"Testimonials","signin":"Sign In/Up","languages":{"english":"English","german":"German","arabic":"Arabic"}}}');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/framer-motion","vendor-chunks/tr46","vendor-chunks/@opentelemetry","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftest-falling-icons%2Fpage&page=%2Ftest-falling-icons%2Fpage&appPaths=%2Ftest-falling-icons%2Fpage&pagePath=private-next-app-dir%2Ftest-falling-icons%2Fpage.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();